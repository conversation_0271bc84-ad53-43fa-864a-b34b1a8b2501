<template>
    <div class=" flex-column height-100">
        <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="8" :lg="6" :xl="4" v-for="item in categoryList"
                    @mouseover="hoverItemId = item.id" @mouseleave="hoverItemId = ''" :key="item.id"
                    @click="handleClickModel(item)" class="b-margin-10 market-item">
                <div class=" border all-padding-16 border-radius-4  justify-between top-bottom-center "
                     :style="{ backgroundImage: (!item.disabled ? 'url(' + searchCategoryBackBackUrl + ')' : ''), backgroundRepeat:'no-repeat' }">
                    <el-tooltip effect="dark" :content="item.name" placement="top">
                        <div class="font-weight-500 font-16 text-ellipsis text-nowrap">
                            {{ item.name }}
                        </div>
                    </el-tooltip>
                    <div class="font-weight-400 t-margin-24  tb-padding-8 font-14 text-center display-flex top-bottom-center left-right-center"
                         :class="[hoverItemId == item.id && !item.disabled ? 'color-blue pointer' : '', item.disabled ? 'go-detail-btn-disabled border not-allow ' : 'go-detail-btn']">
                        <Icon icon="icon-a-huaban264" v-show="!item.disabled"
                              :color="hoverItemId == item.id ? commonColor.mainBlue : commonColor.mainBlack"
                              class="r-margin-5" />
                        {{ item.disabled ? '未开通' : '前往查看' }}
                    </div>

                </div>


            </el-col>
        </el-row>
    </div>

    <el-dialog v-model="categoryDialogVisible" :title="chartData?.name" fullscreen>
        <div class="display-flex" style="height: calc(100vh - 100px)">
            <div class="left flex-1 border-right relative">
                <categoryTreeChat :chartData="chartData" @nodeClick="nodeClick" />
            </div>
            <div class="right com-padding-16">
                <el-scrollbar>
                    <div v-if="nodeDetail" class="all-padding-16">
                        <div class="font-20 bold">{{ nodeDetail?.name || '-' }}</div>
                        <div class="t-margin-10">
                            <el-row v-if="templeteIsLoading">
                                <el-col :span="24"
                                        class="formwork-item border tb-padding-16 lr-padding-20 border-radius-6"
                                        v-for="item in templeteList" :key="item.id">
                                    <div class="b-margin-12 bold">
                                        {{ item.name }}
                                    </div>
                                    <div class="b-margin-16 font-14">
                                        <span class="font-four-level">使用次数：</span>
                                        {{ item.useNum }}
                                    </div>
                                    <div>
                                        <el-button type="primary" @click="jumpMoreSearch(item)">使用</el-button>
                                    </div>
                                </el-col>
                            </el-row>

                            <el-skeleton v-else :rows="5" animated />
                        </div>
                    </div>
                    <el-empty v-if="(!nodeDetail || !templeteList.length) && templeteIsLoading" description="暂无模板数据" />

                </el-scrollbar>

            </div>

        </div>

    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, onUpdated, onUnmounted, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import type { ISearchGetCategoryItem, ISearchGetCategoryResponse, ISearchGetTemplateItem, ISearchGetTemplateResponse } from '@/types/company'

import aicService from '@/service/aicService'
import searchCategoryBackBackUrl from '@/assets/images/search-model-category-back.png'


import categoryTreeChat from '@/views/operations/project-classification/components/TreeEchart.vue'

const router = useRouter()

const hoverItemId = ref('')
const instance = getCurrentInstance()
const commonColor = instance?.appContext.config.globalProperties.$commom.color

const categoryList: Ref<ISearchGetCategoryItem[]> = ref([])
const getCategoryList = () => {
    aicService.modelGetCategory({}).then((res: ISearchGetCategoryResponse) => {

        // if (res.data) { }

        categoryList.value = res.data.sort((a, b) => {
            if (!a.disabled && b.disabled) {
                return -1
            } else if (a.disabled && !b.disabled) {
                return 1
            } else {
                return 0
            }
        })

        console.log(categoryList.value)
    })
}

const categoryDialogVisible: Ref<boolean> = ref(false)

const chartData: Ref<ISearchGetCategoryItem | null> = ref(null)
const handleClickModel = (item: ISearchGetCategoryItem) => {
    if (item.disabled) {
        return
    }
    categoryDialogVisible.value = true

    chartData.value = item
    nodeClick(item)
}

const nodeDetail: Ref<ISearchGetCategoryItem | null> = ref(null)

const templeteList: Ref<ISearchGetTemplateItem[]> = ref([])

const modelLoading: Ref<boolean> = ref(false)
const nodeClick = (node: ISearchGetCategoryItem) => {
    nodeDetail.value = node
    templeteList.value = []
    modelLoading.value = true
    getTemplete(node.id)
}


const jumpMoreSearch = (item: ISearchGetTemplateItem) => {
    aicService.searchUpdateTemplate({ templateId: item.id, useTag: true })
    categoryDialogVisible.value = false
    router.push({
        name: 'more-search-company',
        params: {
            templeteId: item.id,
            type: 'market'
        },
    })
}

const templeteIsLoading: Ref<boolean> = ref(false)

const getTemplete = (templateCategoryId: string) => {
    templeteIsLoading.value = false
    aicService.searchGetTemplate({
        searchType: '1',
        templateCategoryId,
        page: 1,
        pageSize: 99999
    }).then((res: ISearchGetTemplateResponse) => {
        templeteList.value = res.data
    }).finally(() => {
        templeteIsLoading.value = true
    })
}

onMounted(() => {
    getCategoryList()
})
onUpdated(() => { })
onUnmounted(() => { })
</script>

<style lang='scss' scoped>
.market-item {
    background-size: cover;

    .go-detail-btn {
        width: 130px;
        border-radius: 375px;
        border: 1px solid var(--main-white-);
        background: linear-gradient(to right, rgba(236, 241, 251, 1) 0%, rgba(250, 253, 255, 1) 100%);
    }

    .go-detail-btn-disabled {
        width: 130px;
        border-radius: 375px;
    }
}

.left {
    height: 100%;
    width: 500px;
}

.right {
    width: 435px;
    height: 100%;
}

.formwork-item {
    margin-bottom: 16px;
    margin-right: 16px;
    background-color: #fff;
    width: 100%;
    height: 100%;
}
</style>