import type { IPaginationResponse, ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface IUserAccountInfo {
    placeholder?: never
}

export interface IUserAccountInfoResponse {
    user: IUserAccountInfoUser
    permissions: string[]
    scopeData: string[]
    channelId: string
    orgs: { id: string; name: string }[]
    tenant: ITenant
}

export interface IUserAccountInfoUser {
    id: string
    username: string
    mobile: string
    password: string
    nickname: string
    status: number
    role: string[]
    roleName: string[]
    createTime: number
    updateTime: number
    orgId: string[]
    defaultOrg: string
    childOrg: string[]
    tenantId: string
    createUser: string
}

export interface ITenant {
    name?: string
    areaCode?: string
    areaName?: string
    openAiPhone?: boolean
    oemKey?: string
}

export interface IUserListResponse {
    id: string
    username: string
    mobile: string
    nickname?: string
}

export interface IUserListRequestParams {
    username: string
    mobile: string
    roleId: string
    status: string
    orgId: string
}

export interface IUserPageListRequest extends IAllRecord {
    mobile?: string
    orgId?: string
    roleId?: string
    status?: string
    username?: string
    page: number
    pageSize: number
}

export interface IPageUserListResponse extends IPaginationResponse {
    data: IPageUserItem[]
}

export interface IPageUserItem {
    childOrg: Record<string, unknown>[]
    createTime: number
    createUser: string
    defaultOrg: string
    id: string
    mobile: string
    mobileConfirmed: number
    nickname: string
    orgId: string[]
    orgNames: string[]
    password: string
    role: string[]
    roleName: string[]
    status: number
    tenantId: string
    tenantName: string
    unionId: string
    updateTime: number
    username: string
}

export interface IUserAddRequestParams {
    mobile: string
    nickname: string
    orgId: string[]
    password: string
    role: string[]
    tenantId: string
    username: string
    smsCode: string
    status: string
}

export interface IUserAddRequest {
    mobile?: string
    nickname: string
    orgId: string[]
    password: string
    role: string[]
    smsCode?: string
    tenantId: string
    username: string
}

export interface IUserEditRequest {
    authMobileDto?: {
        code: string
        mobile: string
    }
    id: string
    nickname: string
    orgId?: string[]
    role: string[]
    status: string
    tenantId?: string
    updatePwd?: {
        newPassword: string
    }
    username: string
}

export interface IUserAddSendCodeRequest extends IAllRecord {
    mobile: string
}

export interface IUserDisableRequest extends IAllRecord {
    ids: string[]
}

export interface IUserDetailParams extends IAllRecord {
    userId?: string
}

export interface IUserGetUserOrgParams extends IAllRecord {
    id: string
}

export interface IUserDetail {
    childOrg: []
    createTime: number
    createUser: string
    defaultOrg: string
    id: string
    mobile: string
    mobileConfirmed: number
    nickname: string
    orgId: []
    orgNames: []
    password: string
    role: []
    roleName: []
    status: number
    tenantId: string
    unionId: string
    updateTime: number
    username: string
}

type CommonObjType = {
    id: string
    name: string
}
export interface IUserGetUserOrgResponse {
    id: string
    nickname: string
    orgId: string[]
    orgs: CommonObjType[]
}
export interface IUserDetailResponse extends ICommonResponse {
    data: IUserDetail
}

export interface IGetOrgNames {
    id: string
    name: string
}
export interface IOrgGetOrgNamesResponse extends ICommonResponse {
    data: IGetOrgNames[]
}

export interface IGetUserNames {
    id: string
    nickname: string
}

export interface IUserGetUserNameResponse extends ICommonResponse {
    data: IGetUserNames[]
}

export interface IExportUserRequest {
    mobile: string
    orgId: string
    roleId: string
    status: string
    username: string
}

export interface IGetUserListParams {
    page: number
    pageSize: number
}

export interface SearchUserResponseItem {
    id: string
    username: string
    mobile: string
    nickname: string
    role: string[]
    roleName: string[]
    status: number
    orgName: string
    tenantName: string
    lastLoginTime: string
}

export interface IUserplatUserPageRequest extends IAllRecord {
    page: number
    pageSize: number
    username?: string
    mobile?: string
    status?: number
    roleId?: string
}

export interface IUserplatUserPageItem {
    childOrg: string[]
    createTime: number
    createUser: string
    defaultOrg: string
    id: string
    mobile: string
    mobileConfirmed: number
    nickname: string
    orgId: string[]
    orgNames: string[]
    password: string
    registerMobile: string
    registerSource: string
    role: string[]
    roleName: string[]
    status: number
    tenantId: string
    tenantName: string
    unionId: string
    updateTime: number
    username: string
}

export interface IUserplatUserPageResponse extends IPaginationResponse {
    data: IUserplatUserPageItem[]
}
