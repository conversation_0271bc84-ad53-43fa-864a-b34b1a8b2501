import commonData from '@/js/common-data'
import store from '@/store'
import type { IMenuResponse } from '@/types/menu'

const check = (v: string) => {
    const { user } = store.state || {}
    const { account } = user || {}
    const { permissions } = account || {}
    return !!permissions?.includes(v)
}

const checkMenu = (v: string) => {
    const { menu } = store.state || {}
    const { menuTree } = menu || {}
    const MenuIds = flattenMenuTree(menuTree)
    return !!MenuIds.includes(v)
}

const flattenMenuTree = (menuTree: IMenuResponse[]): string[] => {
    let result: string[] = []
    menuTree.forEach(node => {
        result.push(node.id)
        if (node.children && node.children.length > 0) {
            result = result.concat(flattenMenuTree(node.children))
        }
    })
    return result
}

export default {
    // 搜招投标
    isSearchBidPermitted(): boolean {
        return check(commonData.permissions.searchBid.value)
    },
    // 搜工厂
    isSearchFactoryPermitted(): boolean {
        return check(commonData.permissions.searchFactory.value)
    },
    // 最新注册
    isRecentRegPermitted(): boolean {
        return check(commonData.permissions.recentReg.value)
    },
    // 线索池-删除
    isLeadDeletePermitted(): boolean {
        return check(commonData.permissions.leadDelete.value)
    },
    // 线索列表-删除
    isLeadlistDeletePermitted(): boolean {
        return check(commonData.permissions.leadlistDelete.value)
    },
    // 线索列表-导出
    isLeadlistExportPermitted(): boolean {
        return check(commonData.permissions.leadlistExport.value)
    },
    // 线索列表-导入
    isLeadlistImportPermitted(): boolean {
        return check(commonData.permissions.leadlistImport.value)
    },
    // 线索列表-新增
    isLeadlistAddPermitted(): boolean {
        return check(commonData.permissions.leadlistAdd.value)
    },
    // 线索列表-发送短信
    isLeadSendMessagePermitted(): boolean {
        return check(commonData.permissions.leadSendMessage.value)
    },
    // 客户公海-删除
    isCompanypoolDeletePermitted(): boolean {
        return check(commonData.permissions.companypoolDelete.value)
    },
    // 全部客户
    isCompanyAllPermitted(): boolean {
        return check(commonData.permissions.companyAll.value)
    },
    // 客户列表-删除
    isCompanyDeletePermitted(): boolean {
        return check(commonData.permissions.companyDelete.value)
    },
    // 客户列表-导出
    isCompanyExportPermitted(): boolean {
        return check(commonData.permissions.companyExport.value)
    },
    // 客户列表-导入
    isCompanyImportPermitted(): boolean {
        return check(commonData.permissions.companyImport.value)
    },
    // 客户列表-新增
    isCompanyAddPermitted(): boolean {
        return check(commonData.permissions.companyAdd.value)
    },
    // 客户列表-发送短信
    isCompanySendMessagePermitted(): boolean {
        return check(commonData.permissions.companySendMessage.value)
    },
    // 开始任务
    isOutboundtaskStartPermitted(): boolean {
        return check(commonData.permissions.outboundtaskStart.value)
    },
    // 编辑任务
    isOutboundtaskEditPermitted(): boolean {
        return check(commonData.permissions.outboundtaskEdit.value)
    },
    // 暂停任务
    isOutboundtaskPausePermitted(): boolean {
        return check(commonData.permissions.outboundtaskPause.value)
    },
    // 终止任务
    isOutboundtaskAbortPermitted(): boolean {
        return check(commonData.permissions.outboundtaskAbort.value)
    },
    // 删除任务
    isOutboundtaskDeletePermitted(): boolean {
        return check(commonData.permissions.outboundtaskDelete.value)
    },
    // 外呼任务详情导出
    isOutboundtaskExportPermitted(): boolean {
        return check(commonData.permissions.outboundtaskExport.value)
    },
    // 外呼客户-导出
    isOutboundExportPermitted(): boolean {
        return check(commonData.permissions.outboundExport.value)
    },
    // 权益列表-导出
    isBenefitsListExportPermitted(): boolean {
        return check(commonData.permissions.benefitsListExport.value)
    },
    // 消费明细-导出
    isBenefitsUsageExportPermitted(): boolean {
        return check(commonData.permissions.benefitsUsageExport.value)
    },
    // 采集记录-导出
    isCollectionRecordsExportPermitted(): boolean {
        return check(commonData.permissions.collectionRecordsExport.value)
    },
    // 新增用户
    isUserAddPermitted(): boolean {
        return check(commonData.permissions.userAdd.value)
    },
    // 新增组织
    isOrgAddPermitted(): boolean {
        return check(commonData.permissions.orgAdd.value)
    },
    // 编辑用户
    isUserEditPermitted(): boolean {
        return check(commonData.permissions.userEdit.value)
    },
    // 编辑组织
    isOrgEditPermitted(): boolean {
        return check(commonData.permissions.orgEdit.value)
    },
    // 删除组织
    isOrgDeletePermitted(): boolean {
        return check(commonData.permissions.orgDelete.value)
    },
    // 角色权限-新增
    isRoleAddPermitted(): boolean {
        return check(commonData.permissions.roleAdd.value)
    },
    // 角色权限-编辑
    isRoleEditPermitted(): boolean {
        return check(commonData.permissions.roleEdit.value)
    },
    // 角色权限-删除
    isRoleDeletePermitted(): boolean {
        return check(commonData.permissions.roleDelete.value)
    },
    // 企业标签-新增
    isTagMenuAddPermitted(): boolean {
        return check(commonData.permissions.tagMenuAdd.value)
    },
    // 企业标签-编辑
    isTagMenuDeletePermitted(): boolean {
        return check(commonData.permissions.tagMenuDelete.value)
    },
    // 企业标签-删除
    isTagMenuEditPermitted(): boolean {
        return check(commonData.permissions.tagMenuEdit.value)
    },
    // 业务配置-新增
    isBusinessAddPermitted(): boolean {
        return check(commonData.permissions.businessAdd.value)
    },
    // 业务配置-编辑
    isBusinessEditPermitted(): boolean {
        return check(commonData.permissions.businessEdit.value)
    },
    // 业务配置-删除
    isBusinessDeletePermitted(): boolean {
        return check(commonData.permissions.businessDelete.value)
    },
    // 线索池-新增
    isLeadpoolAddPermitted(): boolean {
        return check(commonData.permissions.leadpoolAdd.value)
    },
    // 线索池-编辑
    isLeadpoolEditPermitted(): boolean {
        return check(commonData.permissions.leadpoolEdit.value)
    },
    // 线索池-删除
    isLeadpoolDeletePermitted(): boolean {
        return check(commonData.permissions.leadpoolDelete.value)
    },
    // 公海-新增
    isPublicpoolAddPermitted(): boolean {
        return check(commonData.permissions.publicpoolAdd.value)
    },
    // 公海-编辑
    isPublicpoolEditPermitted(): boolean {
        return check(commonData.permissions.publicpoolEdit.value)
    },
    // 公海-删除
    isPublicpoolDeletePermitted(): boolean {
        return check(commonData.permissions.publicpoolDelete.value)
    },
    // 租户管理-新增
    isTenantAddPermitted(): boolean {
        return check(commonData.permissions.tenantAdd.value)
    },
    // 转移新线索
    isTransferNewLeadPermitted(): boolean {
        return check(commonData.permissions.transferNewLead.value)
    },
    // 查看联系方式
    isContactViewPermitted(): boolean {
        return check(commonData.permissions.contactView.value)
    },
    // 发送短信
    isSmsSendPermitted(): boolean {
        return check(commonData.permissions.smsSend.value)
    },
    // 更新联系方式
    isContactUpdatePermitted(): boolean {
        return check(commonData.permissions.contactUpdate.value)
    },
    // 发起智能外呼
    isDialingTaskCreatePermitted(): boolean {
        return check(commonData.permissions.dialingTaskCreate.value)
    },
    // 菜单权限-标签管理
    isShowTagManagementMenu(): boolean {
        return checkMenu(commonData.menus.crmTag.id)
    },
    // 菜单权限-任务管理
    isShowTaskManagementMenu(): boolean {
        return checkMenu(commonData.menus.taskManagement.id)
    }
}
