<script lang="ts" setup>
import { ref, inject, reactive } from 'vue'
import aicService from '@/service/aicService'
import type { Ref } from 'vue'
import type { IStandardInfoOverViewResponseItem, DraftingUnitsType } from '@/types/model'
import type { IGetTradeMarkGetDetail, IGetStandardInfoOverView } from '@/types/aic'
const props = defineProps<{
    row: IStandardInfoOverViewResponseItem
}>()

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const dialogVisible = ref(false)
const tradeMarkDetail = ref<IGetTradeMarkGetDetail>()
const tableData = ref<DraftingUnitsType[]>()
let pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
const loading = ref(false)
const handleOpenDetail = () => {
    dialogVisible.value = true
    getTableData(props.row)
}
const getTableData = (row: IStandardInfoOverViewResponseItem) => {
    if (!row.releaseDate) return
    const utcDate = new Date(`${row.releaseDate} 00:00:00`)
    const timestamp = utcDate.getTime()
    loading.value = true
    aicService
        .getGsGetDetail({
            socialCreditCode: socialCreditCode.value,
            standardNumber: row.standardNumber,
            page: pageInfo.page,
            releaseDate: timestamp,
            moduleName: 'draftUnitStandard',
        })
        .then((res) => {
            console.log('StandardInfoOverViewResponse', res)
            const { errCode, data } = res || {}
            const { items, total } = (data as IGetStandardInfoOverView) || {}
            if (errCode === 0) {
                loading.value = false
                tableData.value = items
                pageInfo.total = total
            } else {
                loading.value = false
            }
        })
        .catch(() => {
            loading.value = false
        })
}
const pageChange = () => {
    getTableData(props.row)
}
</script>
<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">{{ props.row.standardName || '-' }}</span>
    <el-dialog v-model="dialogVisible" title="标准信息详情" append-to-body style="height: 700px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <!-- 标准详情 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >标准详情</el-col
                    >
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准名称</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        props.row.standardName || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准状态
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        props.row.standardState || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.standardNumber || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >发布日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.releaseDate || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >实施日期</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.implementationDate || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >废止日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准级别</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.standardRank || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准性质
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ props.row.standardProperty || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >标准类别</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ props.row.standardClass || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >制修订
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >中国标准分类号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.chinaStandardClassification || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >国际标准分类号
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.internationStandardClassification || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >技术归口</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >批准发布部门
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >归口单位</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        > {{ props.row.focalUnit || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >执行单位
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ props.row.executiveUnit || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >主管部门</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ props.row.competentDepartment || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >行业分类
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >国民经济分类</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.citizenEconomicClassification || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >是否包含专利信息
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        props.row.hasPatentInformation ? '是' : '否'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >代替以下标准</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >被以下标准代替
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >团体名称</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.groupName || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >适用范围</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.scopeOfApplication || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >主要技术内容</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.technicalContent }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >执行该标准的产品信息</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.markStatusClean || '-' }}
                    </el-col>
                </el-row>
            </div>
            <!-- 备案信息 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >备案信息</el-col
                    >
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >备案号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >备案日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        '-'
                    }}</el-col>
                </el-row>
            </div>
            <!-- 起草单位 -->
            <div class="b-margin-24">
                <el-table v-loading="loading" :data="tableData">
                    <el-table-column prop="name" label="起草单位"></el-table-column>
                </el-table>
                <div class="display-flex" style="flex-direction: row-reverse">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, prev, pager, next"
                        @change="pageChange"
                    />
                </div>
            </div>
            <!-- 起草人 -->
            <div>
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >起草人</el-col
                    >
                </el-row>
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <span class="t-margin-2" v-for="(p, i) in props.row.drafter" :key="i">{{ p }}
                            <span v-if="i !== props.row.drafter.length-1">、</span>
                        </span>
                    </el-col>
                </el-row>
            </div>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
