<template>
    <el-form :inline="true" :model="formJYHY" >
        <el-form-item class="full-width">
            <div class="flex-column">
                <span>域名</span>
                <div class="display-flex">
                    <div class="mb-2 ml-4">
                        <el-radio-group v-model="domainProtocol" style="min-width: 150px;">
                            <el-radio value="1" size="large">http</el-radio>
                            <el-radio value="2" size="large">https</el-radio>
                        </el-radio-group>
                    </div>
                    <el-input v-model="formJYHY.domain" placeholder="请输入域名" style="width: 500px">
                    </el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>SSL证书
                    <span class="color-three-grey font-14">(.key)</span>
                </span>
                <el-upload
                    v-if="!domainKey"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept=".key"
                    :show-file-list="false"
                    :on-success="handleKeySuccess"
                    :on-progress="handleKeyProgress"
                    :on-error="onError"
                    :on-remove="handleKeyRemove"
                    :before-upload="beforeFlieUpload"
                >
                    <el-icon v-if="domainKeyLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="width-100 display-flex top-bottom-center space-between ">
                    <span class="text-ellipsis text-nowrap" style="width: 200px;" :title="domainKeyOriginName">{{ domainKeyOriginName }}</span>
                    <el-icon class="pointer" @click="deleteDomainKey"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>SSL证书
                    <span class="color-three-grey font-14">(.pem)</span>
                </span>
                <el-upload
                    v-if="!domainPem"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept=".pem"
                    :show-file-list="false"
                    :on-success="handlePemSuccess"
                    :on-progress="handlePemProgress"
                    :on-error="onError"
                    :on-remove="handlePemRemove"
                    :before-upload="beforeFlieUpload"
                >
                    <el-icon v-if="domainPemLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="width-100 display-flex top-bottom-center space-between ">
                    <span class="text-ellipsis text-nowrap" style="width: 200px;" :title="domainPemOriginName">{{ domainPemOriginName }}</span>
                    <el-icon class="pointer" @click="deleteDomainPem"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>首页logo</span>
                <el-upload
                    v-if="!mpLogoUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleMpLogoProgress"
                    :on-success="handleMpLogoSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="mpLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="mpLogoUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[mpLogoUrl] />
                    <el-icon class="close-icon" @click="deleteMpLogo"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>首页顶部图</span>
                <el-upload
                    v-if="!mpHomeBannerUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleMpHomeBannerProgress"
                    :on-success="handleMpHomeBannerSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="mpHomeBannerLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="mpHomeBannerUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[mpHomeBannerUrl] />
                    <el-icon class="close-icon" @click="deleteMpHomeBanner"><Close /></el-icon>
                </div>
            </div>
        
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>首页搜索框下方图</span>
                <el-upload
                    v-if="!mpHomeSpecUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleMpHomeSpecProgress"
                    :on-success="handleMpHomeSpecSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="mpHomeSpecLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="mpHomeSpecUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[mpHomeSpecUrl] />
                    <el-icon class="close-icon" @click="deleteMpHomeSpec"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="display-flex flex-column">
                <span >是否开始精简授权</span>
                <el-switch v-model="formJYHY.easyCollect" />
            </div>
        </el-form-item>
        <el-form-item>
            <div class="display-flex flex-column">
                <span>是否开启票易融</span>
                <el-switch v-model="formJYHY.pyr" />
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span >
                    业务进件二维码海报图
                </span>
                <el-upload
                    v-if="!mpQrCode"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleJinrongEduApplyBackimgProgress"
                    :on-success="handleJinrongEduApplyBackimgSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="mpQrCodeLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="mpQrCode" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[mpQrCode] />
                    <el-icon class="close-icon" @click="deletempQrCode"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>业务进件分享图</span>
                <el-upload
                    v-if="!qrCodeImg"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleJinrongEduShareBackimgProgress"
                    :on-success="handleJinrongEduShareBackimgSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="qrCodeImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="qrCodeImg" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[qrCodeImg] />
                    <el-icon class="close-icon" @click="deleteQrCodeImg"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span class="color-two-grey font-16">
                    业务进件二维码信息
                    <span class="color-three-grey font-14">(大小，距离顶部，距离左边(50,0,0))</span>
                </span>
                <el-input
                    v-model="formJYHY.jinrongEduApplyQrcode"
                    placeholder="请输大小，距离顶部，距离左边(50,0,0)"
                ></el-input>
            </div>
        </el-form-item>
    </el-form>
</template>

<script lang='ts' setup>
import { ref, watch, nextTick } from 'vue'
import type { UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { JYHY } from '@/types/OEM'
import fileService from '@/service/fileService'
import type { FileIns, FileUploadResponse } from '@/types/lead'

const props = defineProps<{
    modelValue: JYHY
    editData: JYHY
}>()
watch(() => props.editData, (newValue) => {
    console.log('newValue123123',newValue)
    nextTick(() => {
        formJYHY.value.domain = newValue.domain || ''
        domainProtocol.value = newValue.domainProtocol === 'https' ? '2' : '1'
        domainKey.value = newValue.domainKey ? fileService.getFileUrl(newValue.domainKey) : ''
        domainKeyOriginName.value = newValue.domainKey ? newValue.domainKey?.split('/')[4] : ''
        domainPem.value = newValue.domainPem ? fileService.getFileUrl(newValue.domainPem) : ''
        domainPemOriginName.value = newValue.domainPem ? newValue.domainPem?.split('/')[4] : ''
        mpLogoUrl.value = newValue.mpLogo ? fileService.getFileUrl(newValue.mpLogo) : ''
        mpHomeBannerUrl.value = newValue.mpHomeBanner ? fileService.getFileUrl(newValue.mpHomeBanner) : ''
        mpHomeSpecUrl.value = newValue.mpHomeSpec ? fileService.getFileUrl(newValue.mpHomeSpec) : ''
        formJYHY.value.easyCollect = newValue.easyCollect || false
        formJYHY.value.pyr = newValue.pyr || false
        mpQrCode.value = newValue.mpQrCode ? fileService.getFileUrl(newValue.mpQrCode) : ''
        qrCodeImg.value = newValue.qrCodeImg ? fileService.getFileUrl(newValue.qrCodeImg) : ''
        formJYHY.value.jinrongEduApplyQrcode = newValue.jinrongEduApplyQrcode || ''
    })
},{immediate: true, deep: true })

const formJYHY = ref<JYHY>(props.modelValue)
watch(formJYHY, (newValue) => {
    emit('update:modelValue', newValue)
})
const emit = defineEmits<{
    (e: 'update:modelValue', value: JYHY): void
}>()

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload-temp`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}


//域名
const domainKeyOriginName = ref('')
const domainPemOriginName = ref('')
const domainKey = ref('')
const domainPem = ref('')
const domainKeyLoading = ref(false)
const domainPemLoading = ref(false)
const domainProtocol = ref('1')
const deleteDomainKey = () => {
    domainKeyOriginName.value = ''
    domainKey.value = ''
}

const deleteDomainPem = () => {
    domainPemOriginName.value = ''
    domainPem.value = ''
}

const handleKeyProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    domainKeyLoading.value = true
}
const handlePemProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    domainPemLoading.value = true
}

const handleKeySuccess = (file: FileUploadResponse) => {
    console.log('handleKeySuccess 事件触发：', file)
    domainKey.value = file.data.link
    domainKeyOriginName.value = file.data.originalName
    domainKeyLoading.value = false
    console.log('KeyFile',domainKey.value)
}

const handleKeyRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles)
    domainKey.value = ''

}
const handlePemSuccess = (file: FileUploadResponse) => {
    console.log('handleKeySuccess 事件触发：', file)
    domainPem.value = file.data.link
    domainPemOriginName.value = file.data.originalName
    domainPemLoading.value = false
    console.log('KeyFile',domainPem.value)
}

const handlePemRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles)
    domainPem.value = ''
}

const onError = ( file: FileIns) => {
    console.log('onError 事件触发：', file)
    ElMessage.error('上传失败')
}
const beforeFlieUpload: UploadProps['beforeUpload'] = () => {
    if(formJYHY.value.domain === ''){
        ElMessage.error('请先填写域名')
        return false
    }
    return true
}

// ======经营慧眼======
// 首页logo上传相关
const mpLogoUrl = ref('')
const mpLogoLoading = ref(false)
const handleMpLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpLogoLoading.value = true
}
const handleMpLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpLogoLoading.value = false
    mpLogoUrl.value = response.data.link
}

// 首页顶部图上传相关
const mpHomeBannerUrl = ref('')
const mpHomeBannerLoading = ref(false)
const handleMpHomeBannerProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeBannerLoading.value = true
}
const handleMpHomeBannerSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeBannerLoading.value = false
    mpHomeBannerUrl.value = response.data.link
}

// 首页搜索框下方图上传相关
const mpHomeSpecUrl = ref('')
const mpHomeSpecLoading = ref(false)
const handleMpHomeSpecProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeSpecLoading.value = true
}
const handleMpHomeSpecSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeSpecLoading.value = false
    mpHomeSpecUrl.value = response.data.link
}

// 业务进件二维码海报图上传相关
const mpQrCode = ref('')
const mpQrCodeLoading = ref(false)
const handleJinrongEduApplyBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpQrCodeLoading.value = true
}
const handleJinrongEduApplyBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpQrCodeLoading.value = false
    mpQrCode.value = response.data.link
}

// 业务进件分享图上传相关
const qrCodeImg = ref('')
const qrCodeImgLoading = ref(false)
const handleJinrongEduShareBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    qrCodeImgLoading.value = true
}
const handleJinrongEduShareBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    qrCodeImgLoading.value = false
    qrCodeImg.value = response.data.link
}
const beforePicUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 5) {
        ElMessage.error('文件大小不能超过5mb!')
        return false
    }
    return true
}
const deleteMpLogo = () => {
    mpLogoUrl.value = ''
}
const deleteMpHomeBanner = () => {
    mpHomeBannerUrl.value = ''
}
const deleteMpHomeSpec = () => {
    mpHomeSpecUrl.value = ''
}

const deletempQrCode = () => {
    mpQrCode.value = ''
}

const deleteQrCodeImg = () => {
    qrCodeImg.value = ''
}

// 经营慧眼相关
watch([mpLogoUrl, mpHomeBannerUrl, mpHomeSpecUrl, mpQrCode, qrCodeImg, domainProtocol, domainKey, domainPem ], () => {
    formJYHY.value.mpLogo = mpLogoUrl.value 
    formJYHY.value.mpHomeBanner = mpHomeBannerUrl.value 
    formJYHY.value.mpHomeSpec = mpHomeSpecUrl.value 
    formJYHY.value.mpQrCode = mpQrCode.value 
    formJYHY.value.qrCodeImg = qrCodeImg.value 
    formJYHY.value.domainProtocol = domainProtocol.value === '1' ? 'http' : 'https'
    formJYHY.value.domainKey = domainKey.value 
    formJYHY.value.domainPem = domainPem.value 
    console.log('formJYHY.value',formJYHY.value)
})

</script>

<style lang='scss' scoped>

.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  color: red;
  cursor: pointer;
  display: none;
}

.image-container {
    position: relative;
    width: 50px;
    height: 50px;
}

.image-container:hover .close-icon {
    display: block; 
}
:deep(.el-form-item){
    width: 45% !important;
    height: 60px !important;
}
:deep(.full-width){
    width: 100% !important;
}

:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

:deep(.upload-demo){
    display: flex;
    width: 300px;
}
:deep(.el-upload-list__item){
    width: 180px;
}

:deep(.inline-upload) {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 20px;
}
</style>