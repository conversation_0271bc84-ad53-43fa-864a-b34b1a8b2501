import router from '@/router'
import type { IAuthLoginResponse } from '@/types/auth'
import type { AuthState, RootState } from '@/types/store'
import type { IOEMConfig } from '@/types/OEM'
import { removeItem, setItem } from '@/utils/storage'
import { type Module } from 'vuex'
import { getDefaultState } from '..'
import authService from '@/service/authService'

export const originalState: AuthState = {
    isLogin: false,
    accessToken: '',
    dataScope: [],
    oemConfig: {
        key: '',
        modules: [],
    },
}

const authModule: Module<AuthState, RootState> = {
    namespaced: true, // 启用命名空间
    state: () => originalState,
    getters: {
        isLogin: (state: AuthState) => state.isLogin,
        accessToken: (state: AuthState) => state.accessToken,
        oemConfig: (state: AuthState) => state.oemConfig,
    },
    mutations: {
        SET_IS_LOGIN(state: AuthState, isLogin: boolean) {
            state.isLogin = isLogin
        },
        LOG_IN_SUCCESS(state: AuthState, accessToken: string) {
            state.accessToken = accessToken
        },
        SET_DATA_SCOPE(state: AuthState, dataScope: Record<string, number>[]) {
            state.dataScope = dataScope
        },
        SET_OEM_CONFIG(state: AuthState, oemConfig: IOEMConfig) {
            state.oemConfig = oemConfig
        },
    },
    actions: {
        setIsLogin({ commit }, isLogin: string) {
            commit('SET_IS_LOGIN', isLogin)
        },
        setOemConfig({ commit }, oemConfig: IOEMConfig) {
            commit('SET_OEM_CONFIG', oemConfig)
        },
        loginSuccess({ commit }, loginRes: IAuthLoginResponse) {
            const { access_token, refresh_token, dataScope } = loginRes || {}
            // 数据持久化
            setItem('access_token', access_token)
            if (refresh_token) {
                setItem('refresh_token', refresh_token)
            }

            // 修改内存状态
            commit('LOG_IN_SUCCESS', access_token)
            commit('SET_IS_LOGIN', true)
            commit('SET_DATA_SCOPE', dataScope)
        },
        logout({ commit }) {
            console.log('dispatch logout')

            window.isDynamicRoutesAdded = false

            const remove = () => {
                // 数据持久化
                removeItem('access_token')
                removeItem('refresh_token')

                console.log('removeItem')

                // 修改内存状态
                commit('LOG_IN_SUCCESS', '')
                commit('SET_IS_LOGIN', false)

                this.replaceState(getDefaultState())
                sessionStorage.removeItem('vuex')
                console.log('redirect to login page')
                router.push('/login')
            }

            try {
                // 登出
                authService
                    .logout()
                    .then(() => {
                        remove()
                    })
                    .catch((err) => {
                        console.log('logout err:' + err)
                        remove()
                    })
                    .finally(() => {
                        remove()
                    })
            } catch (error) {
                console.log('logout error:' + error)
                remove()
            }
        },
    },
}

export default authModule
