<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { IPageInfo } from '@/types/service'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
const props = defineProps<{
    allTableData: IRiskEntListItem[] | []
    riskTypeList: IGetRiskTypeData[] | []
}>()
const columns = [
    {
        prop: 'typeName',
        label: '风险类型',
        width: '135'
    },
    {
        prop: 'content',
        label: '风险内容',

    },
    {
        prop: 'sortTime',
        label: '风险发生日期',
        width:'135'
    },
]
const selectedType = ref([])
// const selectedDate = ref([])
const tableData = ref<IRiskEntListItem[]>([])
const tableLoading = ref(false)
const pageInfo = ref<IPageInfo>({
    page: 1,
    pageSize: 5,
    total: 0,
})
const dealTableData = () => {
    tableLoading.value = true
    
    if (!selectedType.value.length) { // 没有选择信用类型
        pageInfo.value.total = props.allTableData.length
        console.log('props.allTableData',props.allTableData)
        
        tableData.value = getPaginatedData(props.allTableData,pageInfo.value.page)
    } else {
        const aa = (props.allTableData).filter((item: IRiskEntListItem) => (selectedType.value as string[]).includes(item.typeName))
        pageInfo.value.total = aa.length
        tableData.value = getPaginatedData(aa,pageInfo.value.page)
    }
    tableLoading.value = false
}
// 数组数据切割分页
function getPaginatedData(allTableData:IRiskEntListItem[], page: number) {
    // 深拷贝原始数组
    const data = JSON.parse(JSON.stringify(allTableData))

    // 计算起始和结束索引
    const startIndex = (page - 1) * 5
    const endIndex = startIndex + 5

    // 返回对应页的数据
    return data.slice(startIndex, endIndex)
}
const pageChange = (val:number) => {
    pageInfo.value.page = val
    dealTableData()
}
watch(() => props.allTableData, () => {
    dealTableData()
}, {
    deep: true,
    immediate: true
})
watch(() => selectedType.value, () => {
    pageInfo.value.page = 1 // 每次改变筛选条件 到要从到头查
    dealTableData()
})
</script>
<template>
    <el-row class="b-margin-12">
        <el-col :span="12">
            <!-- <div class="display-flex top-bottom-center r-margin-16">
                <div class="w-80 color-two-grey font-16">时间筛选: </div>
                <div class="flex-1">
                    <el-date-picker
                        style="width: 100%; box-sizing: border-box"
                        type="daterange"
                        value-format="x"
                        unlink-panels
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        v-model="selectedDate"
                        clearable
                        @change="handleDateChange"
                    />
                </div>
            </div> -->
        </el-col>
        <el-col :span="12">
            <div class="display-flex top-bottom-center">
                <div class="w-80 color-two-grey font-16">风险类型: </div>
                <div class="flex-1">
                    <el-select
                        v-model="selectedType"
                        filterable
                        placeholder="请选择信用类型"
                        style="width: 100%"
                        clearable
                        :multiple="true"
                    >
                        <el-option v-for="(item, index) in riskTypeList" :key="index" :label="item.name" :value="item.name" />
                    </el-select>
                </div>
            </div>
        </el-col>
    </el-row>
    <div>
        <el-table
            :data="tableData"
            style="width: 100%"
            header-row-class-name="color-black font-16 font-weight-500"
            row-class-name="color-black font-16 font-weight-400"
            v-loading="tableLoading"
        >
            <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
                <template #default="scope">
                    <div>{{ scope.row[item.prop] || '-' }}</div>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="t-margin-16 display-flex justify-flex-end">
        <el-pagination
            v-model:currentPage="pageInfo.page"
            v-model:page-size="pageInfo.pageSize"
            :total="pageInfo.total"
            layout="total, prev, pager, next, jumper"
            @change="pageChange"
        />
    </div>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
</style>
