<script lang="ts" setup>
import { ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { LineChartOption } from '@/types/echart'

type AllData = {
    [key: string]: string[]
}
const props = defineProps<{
    data: AllData
}>()

const chartRef = ref(null)
const lineOption = ref<LineChartOption>({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow',
        },
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
    },
    xAxis: [
        {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            axisTick: {
                alignWithLabel: true,
            },
        },
    ],
    yAxis: [
        {
            type: 'value',
        },
    ],
    series: [
        {
            name: '涉诉企业行业数量',
            type: 'bar',
            barWidth: '60%',
            data: [10, 52, 200, 334, 390, 330, 220],
        },
    ],
})
const setChart = () => {
    const myChart = echarts.init(chartRef.value)
    myChart.setOption(lineOption.value)
}
const showChart=ref(true)
const dealData = (data: AllData) => {
    console.log('涉诉企业行业分析props', data['1319582614684672'], data['1320368421733376'])
    if (data['1319582614684672'] && data['1320368421733376']) {
        showChart.value=true
    } else {
        showChart.value=false
    }
    
    lineOption.value.xAxis[0].data = data['1319582614684672']
    lineOption.value.series[0].data = data['1320368421733376']
    setChart()
}
watch(
    () => props.data,
    (newVal) => {
        dealData(newVal)
    }
)
</script>
<template>
    <div class="width-100 height-100 back-color-white border-radius-8 all-padding-16 display-flex flex-column">
        <div class="font-16 color-black font-weight-500">涉诉企业行业分析</div>
        <div class="flex-1">
            <div v-if="showChart" class="width-100 height-100" ref="chartRef"></div>
            <div v-else class="width-100 height-100 no-data"></div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
</style>
