<template>
    <div class="">
        <div class="hight-search-box" :class="{ 'collapse-class': collapseFlag }">
            <div class="display-flex back-color-white height-100 overflow-y-scroll">
                <div class="lr-padding-16 t-padding-8 flex-1 relative">
                    <div class="display-flex gap-24 justify-end">
                        <el-popover
                            @after-enter="showMarketPopover = true"
                            @after-leave="showMarketPopover = false"
                            placement="bottom"
                            trigger="click"
                            width="360"
                        >
                            <template #reference>
                                <div class="display-flex gap-4 pointer">
                                    <Icon icon="icon-chanpin" :color="showMarketPopover ? '#1966FF' : ''" />
                                    <span :class="showMarketPopover ? 'color-blue' : ''">产业市场</span>
                                    <el-icon v-if="!showMarketPopover"><ArrowDown /></el-icon>
                                    <el-icon v-else><ArrowUp color="#1966FF" /></el-icon>
                                </div>
                            </template>
                            <div>
                                <el-input v-model="marketModelName" class="b-margin-4" placeholder="请输入模板名称">
                                    <template #prefix>
                                        <Icon icon="icon-a-chazhaoqiye"></Icon>
                                    </template>
                                </el-input>
                                <el-scrollbar max-height="400">
                                    <div
                                        @click="showCategoryDialog(item)"
                                        v-for="item in marketFilterList"
                                        :key="item.id"
                                        v-show="marketFilterList.length"
                                        :class="!item.disabled ? 'pointer' : 'color-two-grey'"
                                        class="category-item border-radius-4 b-margin-12 all-padding-16 display-flex flex-column"
                                    >
                                        <div class="flex-grow-1">{{ item.name }}</div>
                                        <div
                                            class="category-item-disabled font-12 t-margin-4"
                                            :class="!item.disabled ? 'color-blue' : 'color-two-grey'"
                                        >
                                            {{ item.disabled ? '未开通' : '已开通' }}
                                        </div>
                                    </div>
                                    <!-- <el-skeleton v-show='!marketFilterList.length' :rows="15" animated /> -->
                                    <div v-show="!marketFilterList.length" class="no-data"></div>
                                </el-scrollbar>
                            </div>
                        </el-popover>
                        <el-popover
                            @after-enter="showShareModelPopover = true"
                            @after-leave="showShareModelPopover = false"
                            placement="bottom"
                            trigger="click"
                            width="360"
                        >
                            <template #reference>
                                <div class="display-flex gap-4 pointer">
                                    <Icon icon="icon-sharemoban" :color="showShareModelPopover ? '#1966FF' : ''" />
                                    <span :class="showShareModelPopover ? 'color-blue' : ''">共享模板</span>
                                    <el-icon v-if="!showShareModelPopover"><ArrowDown /></el-icon>
                                    <el-icon v-else><ArrowUp color="#1966FF" /></el-icon>
                                </div>
                            </template>
                            <div>
                                <el-input v-model="shareModelName" class="b-margin-4" placeholder="请输入模板名称">
                                    <template #prefix>
                                        <Icon icon="icon-a-chazhaoqiye"></Icon>
                                    </template>
                                </el-input>
                                <el-scrollbar>
                                    <div
                                        v-for="item in shareFilterList"
                                        :key="item.id"
                                        :id="'save-' + item.id"
                                        @click="
                                            ((activeTemplete = JSON.parse(JSON.stringify(item))), (isShareModel = true))
                                        "
                                        class="pointer border-radius-4 b-margin-12 all-padding-16 display-flex flex-column"
                                        :class="
                                            item.id === activeTemplete?.id
                                                ? 'color-white back-color-avatar'
                                                : 'category-item'
                                        "
                                    >
                                        <div class="flex-grow-1">{{ item.name }}</div>
                                        <!-- <div class="category-item-disabled font-12 t-margin-8 display-flex space-between">
                                            <div @click.stop="editTempleteName(item)">
                                                <Icon icon="icon-a-huaban321"
                                                    :color="item.id === activeTemplete?.id ? '#fff' : ''" size="18px"
                                                    class="r-margin-5" />
                                            </div>
                                            <div @click.stop="delTemplete(item)">
                                                <Icon icon="icon-icon_delete"
                                                    :color="item.id === activeTemplete?.id ? '#fff' : ''" size="18px"
                                                    class="r-margin-5" />
                                            </div>
                                        </div> -->
                                    </div>
                                    <el-empty v-if="!shareFilterList.length" :description="'暂无模板'"></el-empty>
                                </el-scrollbar>
                            </div>
                        </el-popover>
                        <el-popover
                            @after-enter="showSavedModelPopover = true"
                            @after-leave="showSavedModelPopover = false"
                            placement="bottom"
                            trigger="click"
                            width="360"
                        >
                            <template #reference>
                                <div class="display-flex gap-4 pointer">
                                    <Icon icon="icon-moban" :color="showSavedModelPopover ? '#1966FF' : ''" />
                                    <span :class="showSavedModelPopover ? 'color-blue' : ''">已保存模板</span>
                                    <el-icon v-if="!showSavedModelPopover"><ArrowDown /></el-icon>
                                    <el-icon v-else><ArrowUp color="#1966FF" /></el-icon>
                                </div>
                            </template>
                            <div>
                                <el-input v-model="saveModelName" class="b-margin-4" placeholder="请输入模板名称">
                                    <template #prefix>
                                        <Icon icon="icon-a-chazhaoqiye"></Icon>
                                    </template>
                                </el-input>
                                <el-scrollbar max-height="400">
                                    <div
                                        v-for="item in saveFilterList"
                                        :key="item.id"
                                        :id="'save-' + item.id"
                                        @click="
                                            ((activeTemplete = JSON.parse(JSON.stringify(item))),
                                            (isShareModel = false))
                                        "
                                        class="pointer border-radius-4 b-margin-12 all-padding-16 display-flex flex-column"
                                        :class="
                                            item.id === activeTemplete?.id
                                                ? 'color-white back-color-avatar'
                                                : 'category-item'
                                        "
                                    >
                                        <div class="flex-grow-1">{{ item.name }}</div>
                                        <div
                                            class="category-item-disabled font-12 t-margin-8 display-flex space-between"
                                        >
                                            <div @click.stop="editTempleteName(item)">
                                                <Icon
                                                    icon="icon-a-huaban321"
                                                    :color="item.id === activeTemplete?.id ? '#fff' : ''"
                                                    size="18px"
                                                    class="r-margin-5"
                                                />
                                            </div>
                                            <div @click.stop="delTemplete(item)">
                                                <Icon
                                                    icon="icon-icon_delete"
                                                    :color="item.id === activeTemplete?.id ? '#fff' : ''"
                                                    size="18px"
                                                    class="r-margin-5"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <el-empty v-if="!saveFilterList.length" :description="'暂无模板'"></el-empty>
                                </el-scrollbar>
                            </div>
                        </el-popover>
                    </div>

                    <HighSearchRules
                        @search="search"
                        :activeTemplete="activeTemplete"
                        @clearTemplete="((activeTemplete = null), (tableData = []))"
                        @updateTemplete="getUserSaveTemplate"
                        :isAdmin="isAdmin"
                        :isShareModel="isShareModel"
                    />
                </div>
            </div>
        </div>
        <div class="display-flex left-right-center">
            <div
                class="back-color-white collapse-item text-center pointer border"
                @click="collapseFlag = !collapseFlag"
            >
                <el-icon color="#1966FF" size="18px"
                    ><CaretTop v-show="!collapseFlag" /> <CaretBottom v-show="collapseFlag"
                /></el-icon>
            </div>
        </div>
        <div class="hight-search-bottom all-padding-16 back-color-white t-margin-16">
            <div class="">
                <div class="flex flex-row gap-16 top-bottom-center b-margin-8">
                    <div class="flex flex-row gap-8 top-bottom-center">
                        <div class="font-14">
                            共<span class="color-blue lr-padding-2 font-weight-600">{{
                                advancePageInfo.realTotal
                            }}</span
                            >结果
                        </div>
                        <div class="w-1 back-color-border h-12"></div>
                        <div class="font-14">
                            已选<span class="color-blue lr-padding-2 font-weight-600">{{
                                multipleSelection.length
                            }}</span
                            >个
                        </div>
                    </div>
                    <div class="flex flex-row gap-16">
                        <el-dropdown v-if="permissionService.isTransferNewLeadPermitted()" placement="bottom-start">
                            <el-button>
                                转CRM
                                <el-icon class="l-margin-8">
                                    <CaretBottom />
                                </el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="transferSelected">转移所选</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-dropdown placement="bottom-start">
                            <el-button>
                                <el-icon>
                                    <Sort />
                                </el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-for="item in sortList"
                                        :key="item.value"
                                        @click="onSortChange(item.value)"
                                    >
                                        {{ item.label }}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <el-table
                    ref="multipleTableRef"
                    header-row-class-name="back-color-table-header"
                    size="large"
                    width="100px"
                    v-loading="loading"
                    :data="tableData"
                    row-key="id"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" :selectable="selectable" />
                    <el-table-column label="号码" width="150" v-if="searchChannelType === 1">
                        <template #default="scope">
                            <div v-if="scope.row.isBuy || !scope.row.socialCreditCode">
                                <span
                                    v-if="scope.row.hasmobile === '1' && scope.row.socialCreditCode"
                                    class="font-16"
                                    >{{ scope.row.contact }}</span
                                >
                                <span v-else>暂无联系方式</span>
                            </div>
                            <div v-else class="color-blue pointer" @click="buyContact(scope.row)">获取号码</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="name" label="企业名称">
                        <template #default="scope">
                            <div
                                :class="scope.row.socialCreditCode ? 'color-blue pointer' : ''"
                                @click="jumpCompanyDetail(scope.row)"
                            >
                                {{ scope.row.name }}
                            </div>
                            <div class="t-margin-12 font-14">
                                {{ scope.row.legalperson || '-' }} | {{ scope.row.esdate || '-' }} |
                                {{ scope.row.regCapDisplay || '-' }}
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column property="esdate" label="成立日期" width="150" />

                    <el-table-column property="legalperson" label="法定代表人" width="150" /> -->
                    <el-table-column property="entstatus" label="营业状态" width="250">
                        <template #default="scope">
                            <el-tag type="success" v-if="scope.row.entstatus">{{
                                scope.row.entstatus
                            }}</el-tag></template
                        >
                    </el-table-column>
                    <!-- <el-table-column property="regCapDisplay" label="注册资金" width="200" /> -->
                    <el-table-column property="officialWebsite" label="网站" />
                    <el-table-column property="contactaddress" label="通讯地址" />
                </el-table>
            </div>
            <el-affix position="bottom" :offset="0">
                <div class="back-color-white display-flex justify-end tb-padding-6 r-padding-16">
                    <el-pagination
                        v-model:currentPage="advancePageInfo.page"
                        v-model:page-size="advancePageInfo.pageSize"
                        :page-sizes="[10, 20, 30, 40, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="advancePageInfo.total"
                        @size-change="handlePageChange"
                        @current-change="handlePageChange"
                    />
                </div>
            </el-affix>
        </div>

        <el-dialog v-model="categoryDialogFlag" title="产业市场" width="87%">
            <IndustryMarket v-if="categoryDialogFlag" :outinFirstCategoryId="firstCategoryId" @useMarket="useMarket" />
        </el-dialog>

        <CompanyDetaiDrawer
            v-model:drawer="companyDetailDrawerVisible"
            v-if="companyDetailDrawerVisible"
            :socialCreditCode="ckCompanyItem.socialCreditCode"
            @refreshList="search(searchCondition)"
        />
        <TransferCrmDialog
            v-model:visible="transferCrmDialogVisible"
            :selected="multipleSelection"
            :success="transferCrmSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, nextTick, watch, computed } from 'vue'
import type { Ref } from 'vue'
import { useRoute } from 'vue-router'
import type {
    conditionItem,
    ISearchGetCategoryResponse,
    ISearchGetCategoryItem,
    ISearchGetTemplateResponse,
    ISearchGetTemplateItem,
    ISearchCompanyItem,
    ICompanyInfo,
} from '@/types/company'
import { useStore } from 'vuex'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import HighSearchRules from '@/components/search/HighSearchRules.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import IndustryMarket from '@/components/search/IndustryMarket.vue'
import CompanyDetaiDrawer from '@/components/search/company-detail-drawer/company-detail-drawer.vue'
import TransferCrmDialog from '@/components/transfer-crm-dialog/TransferCrmDialog.vue'
import permissionService from '@/service/permissionService'

const route = useRoute()

const templeteId = ref(route.params.templeteId as string)
const type: Ref<string> = ref(route.params.type as string)

console.log(route.params)

const activeType: Ref<string> = ref('market')
const activeTemplete: Ref<ISearchGetTemplateItem | null> = ref(null)

const companyDetailDrawerVisible: Ref<boolean> = ref(false)

const transferCrmDialogVisible = ref(false)

const multipleTableRef = ref()

const collapseFlag = ref(false)

const sortList = [
    {
        label: '默认排序',
        value: 0,
    },
    {
        label: '成立时间-降序',
        value: 1,
    },
    {
        label: '成立时间-升序',
        value: 2,
    },
    {
        label: '注册资本-降序',
        value: 3,
    },
    {
        label: '注册资本-升序',
        value: 4,
    },
]

// popover相关
// 模糊匹配列表
const marketModelName = ref('')
const shareModelName = ref('')
const saveModelName = ref('')
const marketFilterList = computed(() => {
    if (!marketModelName.value) {
        return categoryList.value
    }
    return categoryList.value.filter((category: ISearchGetCategoryItem) =>
        category.name.includes(marketModelName.value)
    )
})

const shareFilterList = computed(() => {
    if (!shareModelName.value) {
        return shareModelList.value
    }
    return shareModelList.value.filter((category: ISearchGetTemplateItem) =>
        category.name.includes(shareModelName.value)
    )
})

const saveFilterList = computed(() => {
    if (!saveModelName.value) {
        return saveModelList.value
    }
    return saveModelList.value.filter((category: ISearchGetTemplateItem) => category.name.includes(saveModelName.value))
})
const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})

const showMarketPopover = ref<boolean>(false)
const showShareModelPopover = ref<boolean>(false)
const showSavedModelPopover = ref<boolean>(false)
watch(
    () => showMarketPopover.value,
    (val) => {
        console.log('showMarketPopover', val)
        if (val) {
            showSavedModelPopover.value = false
            showShareModelPopover.value = false
        }
    }
)
watch(
    () => showShareModelPopover.value,
    (val) => {
        console.log('showSavedModelPopover', val)
        if (val) {
            showMarketPopover.value = false
            showSavedModelPopover.value = false
        }
    }
)
watch(
    () => showSavedModelPopover.value,
    (val) => {
        console.log('showSavedModelPopover', val)
        if (val) {
            showMarketPopover.value = false
            showShareModelPopover.value = false
        }
    }
)

const selectable = (row: ICompanyInfo) => {
    return row.socialCreditCode
}

const getTemplateById = () => {
    //根据id获取指定模板
    aicService
        .searchGetTemplate({
            templateId: templeteId.value,
            searchType: '1',
            page: 1,
            pageSize: 20,
        })
        .then((res) => {
            if (res.data.length) {
                activeTemplete.value = res.data[0]
            }

            console.log('marketRes', res)
        })
}

if (type.value) {
    //外部跳转进入
    activeType.value = type.value

    if (type.value === 'market' && templeteId.value) {
        //获取产业市场的模板
        getTemplateById()
    }
}

const loading: Ref<boolean> = ref(false)

const tableData: Ref = ref([])

const searchChannelType: Ref<number | null> = ref(null)

const multipleSelection = ref<ICompanyInfo[]>([])

const advancePageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
    realTotal: 0,
})

const searchCondition: Ref<conditionItem> = ref({} as conditionItem)

const handlePageChange = () => {
    search(searchCondition.value)
}

const sortBy: Ref<number> = ref(0)

const onSortChange = (v: number) => {
    sortBy.value = v
    search(searchCondition.value)
}

const search = (condition: conditionItem) => {
    console.log(111)
    searchCondition.value = condition
    loading.value = true
    aicService
        .searchAdvancedSearch({
            condition,
            page: advancePageInfo.page,
            pageSize: advancePageInfo.pageSize,
            sortBy: sortBy.value,
        })
        .then((res) => {
            loading.value = false
            tableData.value = res.data
            searchChannelType.value = res.channelType
            advancePageInfo.total = res.total
            advancePageInfo.realTotal = res.realTotal
        })
}

const categoryList: Ref<ISearchGetCategoryItem[]> = ref([])
const saveModelList: Ref<ISearchGetTemplateItem[]> = ref([])

const userTempletePageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})

const getCategoryList = () => {
    aicService.modelGetCategory({}).then((res: ISearchGetCategoryResponse) => {
        categoryList.value = res.data.sort((a, b) => {
            if (!a.disabled && b.disabled) {
                return -1
            } else if (a.disabled && !b.disabled) {
                return 1
            } else {
                return 0
            }
        })
    })
}

const editTempleteName = (item: ISearchGetTemplateItem) => {
    ElMessageBox.prompt('请输入模板名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(?!\s*$).+/,
        inputErrorMessage: '模板名称不能为空',
    })
        .then(({ value }) => {
            //存模板逻辑
            aicService
                .searchUpdateTemplate({
                    name: value,
                    templateId: item.id,
                })
                .then(() => {
                    ElMessage({
                        type: 'success',
                        message: '保存成功',
                    })
                    item.name = value
                })
            console.log(value)
        })
        .catch(() => {})
}

const delTemplete = (item: ISearchGetTemplateItem) => {
    ElMessageBox.confirm('是否要删除模板?', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            aicService.searchDeleteTemplate(item.id).then(() => {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                getUserSaveTemplate()
            })
        })
        .catch(() => {})
}

const getUserSaveTemplate = () => {
    aicService
        .searchGetTemplate({
            searchType: '2',
            page: 1,
            pageSize: 999999,
        })
        .then((res: ISearchGetTemplateResponse) => {
            if (res.errCode) {
                saveModelList.value = []
                ElMessage({
                    type: 'error',
                    message: '获取用户保存的模板失败',
                })
                return
            }

            saveModelList.value = res.data
            userTempletePageInfo.total = res.total

            if (type.value === 'save' && templeteId.value) {
                nextTick(() => {
                    let element = document.querySelector(`#save-${templeteId.value}`)
                    if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    }
                })

                let res = saveModelList.value.find((item) => {
                    return item.id === templeteId.value
                })
                if (res) {
                    console.log('getUserSaveTemplate', res)
                    activeTemplete.value = res
                }
            }
        })
}

const shareModelList: Ref<ISearchGetTemplateItem[]> = ref([])
const getUserShareTemplate = () => {
    aicService
        .searchGetEntTemplate({
            page: 1,
            pageSize: 99999,
        })
        .then((res: ISearchGetTemplateResponse) => {
            console.log('getUserShareTemplate', res)
            console.log('query123', templeteId.value)
            const { data, errCode } = res
            if (errCode === 0) {
                shareModelList.value = data
                let response = shareModelList.value.find((item) => {
                    return item.id === templeteId.value
                })
                if (response) {
                    activeTemplete.value = response
                }
            } else {
                shareModelList.value = []
                ElMessage({
                    type: 'error',
                    message: '获取用户分享的模板失败',
                })
                return
            }
        })
}

const categoryDialogFlag: Ref<boolean> = ref(false)
const firstCategoryId: Ref<string> = ref('')

const showCategoryDialog = (item: ISearchGetCategoryItem) => {
    if (item.disabled) {
        return
    }
    categoryDialogFlag.value = true
    firstCategoryId.value = item.id
}

const useMarket = (item: ISearchGetTemplateItem) => {
    templeteId.value = item.id
    getTemplateById()

    categoryDialogFlag.value = false
}

const ckCompanyItem: Ref<ISearchCompanyItem> = ref({} as ISearchCompanyItem)

const jumpCompanyDetail = (item: ISearchCompanyItem) => {
    if (!item.socialCreditCode) {
        ElMessage({
            type: 'error',
            message: '该企业暂无详情',
        })
        return
    }
    ckCompanyItem.value = item
    companyDetailDrawerVisible.value = true
}

const buyContact = (item: ISearchCompanyItem) => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService
                .orderBuyLegal({
                    socialCreditCode: item.socialCreditCode,
                    companyName: item.companyName,
                    serviceKey: 'xs',
                })
                .then((res) => {
                    item.isBuy = true
                    item.contact = res.contacts[0].content
                    ElMessage({
                        message: '使用成功',
                        type: 'success',
                    })
                })
        })
        .catch(() => {})
}

const transferSelected = () => {
    if (!multipleSelection.value.length) {
        ElMessage({
            type: 'error',
            message: '请选择要转移的企业',
        })
        return
    }
    transferCrmDialogVisible.value = true
}

const transferCrmSuccess = () => {
    multipleTableRef.value.clearSelection()
    multipleSelection.value = []
}

const handleSelectionChange = (val: ICompanyInfo[]) => {
    multipleSelection.value = val
}

const isShareModel = ref<boolean>(false)
onMounted(() => {
    if (type.value === 'share') {
        isShareModel.value = true
    } else {
        isShareModel.value = false
    }
    console.log('1231231', isShareModel.value)
    getCategoryList()
    getUserSaveTemplate()
    getUserShareTemplate()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}

.right-box {
    &:deep .el-scrollbar__view {
        height: 100%;
    }
}

.category-item {
    background-color: var(--second-blue);
}

.category-item-disabled {
    height: 20px;
}

.hight-search-box {
    transition: height 0.5s ease;
    background-color: var(--main-background-grey-);
}

.back-grey {
    background-color: var(--main-background-grey-);
}

.hight-search-bottom {
    .back-color-table-header {
        background-color: var(--table-bg-) !important;
        height: 30px;
    }
}

.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: 240px 200px;
    background-repeat: no-repeat;
    background-position-x: center;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.collapse-item {
    width: 100px;
    border-top: none;
    margin-top: -3px;
    border-radius: 0 0 6px 6px;
}
.collapse-item:hover {
    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
}

@keyframes shakeHeight {
    0% {
        height: 100%;
    }

    80%,
    90% {
        height: 190px; /* 稍微增大的高度 */
    }

    100% {
        height: 200px;
    }
}

.collapse-class {
    animation: shakeHeight 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
    transform: translate3d(0, 0, 0);
}
</style>
