import createPersistedState from 'vuex-persistedstate'
import { createStore, Store } from 'vuex'
import type { RootState } from '@/types/store.js'
import auth, { originalState as authState } from './modules/auth.ts'
import user, { originalState as userState } from './modules/user.ts'
import app, { originalState as appState } from './modules/app.ts'
import enterprise, { originalState as enterpriseState } from './modules/enterprise.ts'
import menu, { originalState as menuTreeState } from './modules/menu.ts'

const store = createStore({
    modules: {
        auth,
        user,
        app,
        enterprise,
        menu,
    },
    plugins: [
        createPersistedState({
            storage: sessionStorage,
        }),
    ],
})

const _store: Store<RootState> = store

// const getDefaultState = () => {
//     return {
//         auth: authState,
//         user: userState,
//         app: appState,
//         enterprise: enterpriseState,
//     }
// }
const getDefaultState = () => {
    return {
        auth: { ...authState },
        user: { ...userState },
        app: { ...appState },
        enterprise: { ...enterpriseState },
        menu: { ...menuTreeState },
    }
}

export default store
export { _store, getDefaultState }
