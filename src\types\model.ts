
import type { IAllRecord } from './record'
export interface IModelJson {
    type: string;
    key: string;
    model: string;
    options: {
        hidden: boolean;
        customClass?: string
    };
    name: string;
    list?: IAllRecord[]
}

export interface IHighSearchRuleItem {
    dataType: string;
    enums: Array<{
        name: string;
        tagValue: string | number
    }>;
    key: string;
    name: string;
    needSearch?: string;
    unit?: string
    levelConfig?: {
        levels: { name: string, title: string }[]
    }
}

export interface IHighSearchRules {
    name: string;
    children: Array<IHighSearchRuleItem>
}
export interface ISearchConditions2 {
    type?: string;//branch leaf 是分组还是单一条件，branch为单一
    operator: string;
    prop: string; //字段名
    name?: string;
    propLabel?: string;//字段展示名
    value: (string | number | string[])[] | string | number;
    children?: ISearchConditions[] | ISearchConditions2;
    dataType?: string;
    valueLabel?: Array<string | number>
    unit?: string
    matching?: string
    id?: string
    maxScore?: number
}


export interface ISearchConditions {
    type?: string;//branch leaf 是分组还是单一条件，branch为单一
    operator: string;
    prop: string; //字段名
    name?: string;
    propLabel?: string;//字段展示名
    value: (string | number | string[])[] | string | number;
    children?: ISearchConditions2[];
    dataType?: string;
    valueLabel?: Array<string | number>
    unit?: string
    matching?: string
    id?: string
    maxScore?: number
}

export interface IStaticConfigResponse {
    name?: string
}

export interface ITradeMarkResponseItem{
    APPLYDATE: string
    APPLYNO: string
    MARKNAME: string
    MARKTYPE: string
    MARKTYPENUM: string
    STATUS: string
    USERIGHTDATEEND: string
    markTypeStr: string
    MARKIMGOSS?: string
}

export interface DraftingUnitsType{
    name: string
    pid: string
}
export interface IStandardInfoOverViewResponseItem{
    chinaStandardClassification: string
    citizenEconomicClassification: string
    competentDepartment: string
    drafter: string[]
    draftingUnits: DraftingUnitsType[]
    edit: string
    executiveUnit: string
    focalUnit: string
    groupName: string
    implementationDate: string
    industryClassification: string
    internationStandardClassification: string
    publishingDepartment: string
    recordNumber: string
    releaseDate: string
    scopeOfApplication: string
    standardClass: string
    standardName:string
    standardNumber:string
    standardProperty: string
    standardRank: string
    standardState: string
    standardSubstitution: string
    substituteStandard: string
    technicalCentring: string
    technicalContent: string
    hasPatentInformation: boolean
}

export interface IWebsiteInformationResponse{
    CHECKDATE: number
    COMPANYNAME: string
    HAVERANK: null | string
    KXSOURCE: number
    PID: string
    RECORD: string
    RECORDID: string
    SITEDOMAIN: string
    SITEHOME: string
    SITEHOMEACCESS: boolean
    SITENAME: string
    SITETYPE: string
    SITE_EQ_COM: boolean
    TAIL_NUM: number
    URL: string
    WEBNAME: string
    domainAge: string
    domainCreateTime: string
    domainExpireTime: string
    seoDescription: string
    seoKeyWords: string
    seoTitle: string
    templateSupplier: string
    totalCollectionBaidu: number
    totalCollectionQihoo: number
    totalCollectionSougou: number
    weightPcBaidu: number
    weightPcQihoo: number
    weightPcSougou: number
    _id: string
}

