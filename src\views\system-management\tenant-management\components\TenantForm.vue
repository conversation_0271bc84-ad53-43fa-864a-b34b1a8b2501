<script lang="ts" setup>
import { reactive, ref, watch, onBeforeMount, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, ElCascader, CascaderNode } from 'element-plus'
import type { IAddTenantParams, ITenantPageItem } from '@/types/tenant'
import type { CatagoryItem } from '@/types/aic'

import systemService from '@/service/systemService'
import aicService from '@/service/aicService'

type areaOptionArr = {
    label: string
    value: string
    children: areaOptionArr[] | []
}

const formRef = ref<FormInstance>()

const props = defineProps<{
    visible: boolean
    from: string
    editValue: Partial<ITenantPageItem>
    isAdmin: boolean
}>()

const dialogVisible = computed(() => {
    return JSON.parse(JSON.stringify(props.visible))
})
const slsj = ref<boolean>(false)
const tenantId = ref('')
const form = reactive<IAddTenantParams>({
    name: '',
    username: '',
    password: '',
    areaCode: '',
    allowProjectIds: [],
})

watch(
    () => props.editValue,
    (newVal) => {
        console.log('editValue', newVal)
        if (Object.keys(newVal).length > 0) {
            const { id, name, areaCode, oemKey, openAiPhone, allowProjectIds,isDemonstrate } = newVal as ITenantPageItem
            tenantId.value = id
            form.name = name
            form.areaCode = areaCode || ''
            form.oemKey = oemKey ? oemKey : ''
            form.openAiPhone = openAiPhone || false
            form.allowProjectIds = allowProjectIds || []
            if(isDemonstrate === '1'){
                slsj.value = true
            }else{
                slsj.value = false
            }
        }
    }, {
        immediate: true
    }
)

const rules = reactive<FormRules<IAddTenantParams>>({
    name: [{ required: true, message: '请输入租户名称', trigger: 'change' }],
    // contact: [
    //     {
    //         required: true,
    //         message: '请输入联系人',
    //         trigger: 'change',
    //     },
    // ],
    // phone: [
    //     {
    //         required: true,
    //         message: '请输入联系电话',
    //         trigger: 'change',
    //     },
    //     {
    //         pattern: /^1[3-9]\d{9}$/,
    //         message: '请输入正确的手机号码',
    //         trigger: 'change',
    //     },
    // ],
    username: [
        {
            required: true,
            message: '请输入用户名',
            trigger: 'change',
        },
        {
            min: 6,
            message: '登录账号长度不能小于6位',
            trigger: 'change',
        },
        {
            max: 12,
            message: '登录账号长度不能大于12位',
            trigger: 'change',
        },
        {
            pattern: /^[A-Za-z0-9]+$/,
            message: '登录账号由英文或数字构成',
            trigger: 'change',
        },
        {
            pattern: /^(?!1[3-9]\d{9}$).+$/,
            message: '登录账号不可为手机号',
            trigger: 'change',
        },
    ],
    password: [
        {
            required: true,
            message: '请输入用户密码',
            trigger: 'change',
        },
        {
            min: 6,
            max: 16,
            message: '密码长度必须在6到16个字符之间',
            trigger: 'blur',
        },
        {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
            message: '密码必须包含字母和数字',
            trigger: 'blur',
        },
    ],
    website: [
        {
            required: true,
            message: '请输入域名网址',
            trigger: 'change',
        },
    ],
    appid: [
        {
            required: true,
            message: '请输入appid',
            trigger:'change'
        }
    ],
    privateKey: [
        {
            required: true,
            message: '请输入appid接口秘钥',
            trigger: 'change'
        }
    ]

})
watch(() => slsj.value, (newVal) => {
    if (newVal) {
        form.isDemonstrate = '1'
    } else {
        form.isDemonstrate = '0'
    }
})
const emit = defineEmits(['closeVisible'])
const handleClose = () => {
    form.name = ''
    form.username = ''
    form.password = ''
    form.areaCode = ''
    form.openAiPhone = false
    form.allowProjectIds = []
    form.isDemonstrate = '0'
    emit('closeVisible')
}
const submitLoading = ref(false)
const submitForm = async (formEl: FormInstance | undefined) => {
    console.log('form', form)
    if (!formEl) return
    submitLoading.value = true
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (props.from === 'add') {
                form.createUserRole = true
                form.menuIds = [
                    'index',
                    'search',
                    'search-company',
                    'com-search-company',
                    'more-search-company',
                    'company-map',
                    'su-crm',
                    'su-crm-lead-pool',
                    'lsu-crm-lead-list',
                    'crm-customer-poll',
                    'su-crm-customer-list',
                    'company-manage',
                    'risk-monitor',
                    'risk-manage',
                    'data-center',
                    'user-overview',
                    'collect-log',
                    'system-management',
                    'mail-list'
                ]
                let addRes = await systemService.tenantAdd(form)
                if (addRes.success === true) {
                    ElMessage.success('新增租户成功')
                    submitLoading.value = false
                    handleClose()
                } else {
                    ElMessage.error(addRes.errMsg)
                    submitLoading.value = false
                }
            } else {
                let obj = {
                    id: tenantId.value,
                    name: form.name,
                    areaCode: form.areaCode,
                    areaName: form.areaName,
                    oemKey: form.oemKey,
                    allowProjectIds: form.allowProjectIds,
                    openAiPhone: form.openAiPhone,
                    isDemonstrate: form.isDemonstrate
                }
                let editRes = await systemService.tenantEdit(obj)
                if (editRes.success === true) {
                    ElMessage.success('操作成功')
                    submitLoading.value = false
                    handleClose()
                } else {
                    ElMessage.error(editRes.errMsg)
                    submitLoading.value = false
                }
            }
        } else {
            console.log('form表单效验不通过', fields)
            submitLoading.value = false
        }
    })
}

const areaList = ref<areaOptionArr[]>([])
const getArea = async () => {
    let areaRes = await aicService.conditionGetData({})
    areaList.value = areaRes.area.map((item) => ({
        label: item.label,
        value: item.value,
        children:
            item.children && item.children.length > 0
                ? item.children.map((child) => ({
                    label: child.label,
                    value: child.value,
                    children: [],
                }))
                : [],
    }))
}

const cascaderProps = {
    emitPath: false,
}

// 获取授权类目
const modelCategoryList=ref<CatagoryItem[]>([])
const getModelCategory = async () => {
    let modelCategoryRes = await aicService.modelGetCategoryList(
        {
            page: 1,
            pageSize: 100
        }
    )
    modelCategoryList.value = modelCategoryRes.data
    
}


onBeforeMount(() => {
    // 获取地区接口
    getArea()
    getModelCategory()
})
const cascaderRef = ref<InstanceType<typeof ElCascader> | null>(null)

const changeArea = () => {
    const chartDom = cascaderRef.value
    if (!chartDom) return // 确保 cascaderRef 已绑定

    const nodes: CascaderNode[] | undefined = chartDom.getCheckedNodes(true) // 获取选中的节点数组
    console.log('nodes', nodes)
    if (nodes) {
        form.areaName = nodes[0].data?.label
    }
}
</script>
<template>
    <!-- 新增弹框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="props.from === 'add' ? '新增' : '修改'"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <el-form class="tenant-form" ref="formRef" :model="form" :rules="rules" label-position="top">
            <el-form-item label="租户名称" prop="name">
                <el-input v-model.trim="form.name" />
            </el-form-item>
            <!-- <el-form-item v-if="props.from === 'add'" label="联系人" prop="contact">
                <el-input v-model.trim="form.contact" />
            </el-form-item>
            <el-form-item v-if="props.from === 'add'" label="联系电话" prop="phone">
                <el-input v-model.trim="form.phone" />
            </el-form-item> -->
            <el-form-item v-if="props.from === 'add'" label="登录账号" prop="username">
                <el-input v-model.trim="form.username" />
            </el-form-item>
            <el-form-item v-if="props.from === 'add'" label="登录密码" prop="password">
                <el-input v-model.trim="form.password" type="password" show-password/>
            </el-form-item>
            <el-form-item label="地区" prop="areaCode">
                <el-cascader
                    ref="cascaderRef"
                    style="width: 100%"
                    v-model="form.areaCode"
                    placeholder="请选择省市区"
                    :options="areaList"
                    :props="cascaderProps"
                    clearable
                    @change="changeArea"
                />
            </el-form-item>
            <el-form-item v-if="props.from === 'edit'" label="OEM KEY" prop="oemKey">
                <el-input v-model.trim="form.oemKey" />
            </el-form-item>
            <el-form-item v-if="(props.from === 'add' || props.from === 'edit') && props.isAdmin" label="授权项目">
                <el-select placeholder="请选择授权项目" v-model="form.allowProjectIds" multiple collapse-tags collapse-tags-tooltip>
                    <el-option
                        v-for="item in modelCategoryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="(props.from === 'add' || props.from === 'edit') && props.isAdmin" label="是否开启示例数据">
                <el-switch v-model="slsj"></el-switch>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)" :loading="submitLoading">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss">
.tenant-form {
    :deep(.el-form-item__label) {
        font-size: 16px;
        color: #666666;
    }
}
</style>
