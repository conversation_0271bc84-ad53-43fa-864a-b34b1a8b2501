import type { AxiosHeaders, AxiosInstance, AxiosRequestHeaders, AxiosResponse } from 'axios'
import type { IAllRecord } from './record'

export interface IAxiosConfig {
    hideError?: boolean
    unlessAuth?: boolean
    headers?: AxiosHeaders
    params?: IAllRecord
    fileType?: 'arraybuffer' | 'blob'
    timeout?: number
    responseType?: 'arraybuffer' | 'blob'
    repeatCancel?: boolean
    fullRes?: boolean
}

// 扩展 AxiosInstance 类型
export interface IAxiosInstance extends AxiosInstance {
    (config: IAxiosConfig): Promise<AxiosResponse>
    (url: string, config?: IAxiosConfig): Promise<AxiosResponse>
    // eslint-disable-next-line
    get<T = any>(url: string, config?: IAxiosConfig): Promise<T>
    // eslint-disable-next-line
    post<T = any>(url: string, data?: any, config?: IAxiosConfig): Promise<T>
    // eslint-disable-next-line
    put<T = any>(url: string, data?: any, config?: IAxiosConfig): Promise<T>
    // eslint-disable-next-line
    delete<T = any>(url: string, config?: IAxiosConfig): Promise<T>
}

export interface IAxiosRequestHeaders extends AxiosRequestHeaders {
    Authorization?: string
    'Captcha-Key'?: string
}

export interface IPaginationResponse {
    errCode: number
    success: boolean
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasPreviousPage: boolean
    hasNextPage: boolean
    lastPage: boolean
    firstPage: boolean
}

export interface ICommonResponse {
    errCode: number
    success: boolean
    errMsg: string
}
