<script setup lang="ts">
import { onMounted, ref } from 'vue'
import indicatorService from '@/service/indicatorService'
import Ssqyhyfx from '@/views/risk-management/risk-monitoring/components/Ssqyhyfx.vue'
import Fxsjqsfb from '@/views/risk-management/risk-monitoring/components/Fxsjqsfb.vue'
import Fxsjdjfb from '@/views/risk-management/risk-monitoring/components/Fxsjdjfb.vue'
import Djztfb from '@/views/risk-management/risk-monitoring/components/Djztfb.vue'
import Jqtfxsjfx from '@/views/risk-management/risk-monitoring/components/Jqtfxsjfx.vue'

type AllData = {
  [key: string]: string[];
}
const allData = ref<AllData>()
const getData = () => {
    indicatorService.getCompareIndicatorData({
        ids: '4008,3001,1010', // 涉诉企业行业  风险级别  登记状态
        socialCreditCode:'xxx'
    }).then((res) => {
        console.log('resresres', res)
        allData.value=res
    })
}

onMounted(() =>{
    getData()
})
</script>

<template>
    <div class="width-100 height-100 display-flex space-between gap-16">
        <div class="width-70">
            <div class="width-100 h-544 b-margin-16">
                <!-- 涉诉企业行业分析 -->
                <Ssqyhyfx :data="allData"></Ssqyhyfx>
            </div>
            <div class="width-100 h-433 display-flex space-between gap-16">
                <div class="width-60">
                    <!-- 风险事件等级分布 -->
                    <Fxsjdjfb :data="allData"></Fxsjdjfb>
                </div>
                <div class="width-40">
                    <!-- 登记状态分布 -->
                    <Djztfb :data="allData"></Djztfb>
                </div>
            </div>
        </div>
        <div class="width-30">
            <div class="width-100 h-544 b-margin-16">
                <!-- 风险事件趋势分析 -->
                <Fxsjqsfb></Fxsjqsfb>
            </div>
            <div class="width-100 h-433">
                <!-- 近七天风险事件分析 -->
                <Jqtfxsjfx></Jqtfxsjfx>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.bg-white {
    background-color: #fff;
}
</style>
