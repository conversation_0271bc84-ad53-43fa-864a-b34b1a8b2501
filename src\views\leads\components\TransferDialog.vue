<script lang='ts' setup>
import { ref, computed } from 'vue'

import TransferPool2Pool from '@/views/leads/components/TransferPool2Pool.vue'
import TransferPool2Person from '@/views/leads/components/TransferPool2Person.vue'
import TransferPerson2Pool from '@/views/leads/components/TransferPerson2Pool.vue'
import TransferPerson2Person from '@/views/leads/components/TransferPerson2Person.vue'
import Transfer2ExistingCustomer from '@/views/leads/components/Transfer2ExistingCustomer.vue'
import crmService from '@/service/crmService'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { ILeadData } from '@/types/lead'

type ShowItem='lead2Person' | 'lead2Pool' | 'customer2Person' | 'customer2Pool' |'turnNewCustomer' |'turnExistingCustomer'
type FromValue = 'leadPool' | 'leadList' | 'customerPool' | 'customerList'
type TurnValue = 'person' | 'pool' | 'newPerson' | 'existingCustomer'
const props = defineProps<{
    from: FromValue
    showConfig: ShowItem[]
    selectedData:ILeadData[]
}>()
const transferPool2PoolVisible = ref(false)
const transferPool2PersonlVisible = ref(false)
const transferPerson2PoolVisible = ref(false)
const transferPerson2PersonVisible = ref(false)
const transfer2ExistingCustomerVisible = ref(false)
const selectedIds = computed(() => {
    if (props.selectedData) {
        return props.selectedData.map(item=>item.id) 
    } else {
        return []
    }
})
const poolId = computed(() => {
    const { poolId } = props.selectedData[0] || {}
    return poolId
})
const handleTurnLead = (type: TurnValue) => {
    if (props.selectedData.length < 1) {
        ElMessage({
            type: 'warning',
            message: `请选择需要转移的${(props.from ==='leadPool' || props.from === 'leadList') ? '线索' : '客户' }`,
        })
        return
    }
    if ((props.from === 'leadPool' && type === 'person') || (props.from === 'customerPool' && type === 'person')) {
        /* 池子转人 */
        transferPool2PersonlVisible.value = true
    } else if ((props.from === 'leadPool' && type === 'pool') || (props.from === 'customerPool' && type === 'pool')) {
        /* 池子转池子 */
        transferPool2PoolVisible.value = true
    } else if ((props.from === 'leadList' && type === 'person') || (props.from === 'customerList' && type === 'person')) {
        /* 人转人 */
        transferPerson2PersonVisible.value = true
    } else if ((props.from === 'leadList' && type === 'pool') || (props.from === 'customerList' && type === 'pool')) {
        /* 人转池子 */
        transferPerson2PoolVisible.value = true
    } else if (props.from === 'leadList' && type === 'newPerson') {
        /* 转新客户 */
        ElMessageBox.confirm(
            '是否确认转为新客户？',
            '确认',
            {
                confirmButtonText: '确认',
                cancelButtonText: '再想想',
                type: 'warning',
            }
        ).then(() => {
            let obj = {
                ids: selectedIds.value,
                transferType: 5,
            }
            crmService.crmTransfer(obj).then(() => {
                ElMessage({
                    type: 'success',
                    message: '转移成功',
                })
                emit('closeVisible')
            })
        })
    } else if (props.from === 'leadList' && type === 'existingCustomer') {
        /* 转已有客户 */
        transfer2ExistingCustomerVisible.value = true
    } 
}
const emit = defineEmits(['closeVisible'])
const handleClose = (val?:string) => {
    transferPool2PoolVisible.value = false
    transferPool2PersonlVisible.value = false
    transferPerson2PoolVisible.value = false
    transferPerson2PersonVisible.value = false
    transfer2ExistingCustomerVisible.value = false
    emit('closeVisible', val)
}
</script>
<template>
    <el-dropdown>
        <el-button class="no-focus-visible color-black">
            转移
            <el-icon class="el-icon--right">
                <CaretBottom />
            </el-icon>
        </el-button>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item v-if="props.showConfig.includes('lead2Person')" @click="handleTurnLead('person')"><span class="color-black">转移线索给他人</span></el-dropdown-item>
                <el-dropdown-item v-if="props.showConfig.includes('lead2Pool')" @click="handleTurnLead('pool')"><span class="color-black">{{ props.from === 'leadPool' ? '转移线索到其他线索池' : '转移线索到线索池' }}</span></el-dropdown-item>
                <el-dropdown-item v-if="props.showConfig.includes('turnNewCustomer')" @click="handleTurnLead('newPerson')"><span class="color-black">转移成新客户</span></el-dropdown-item>
                <el-dropdown-item v-if="props.showConfig.includes('turnExistingCustomer')" @click="handleTurnLead('existingCustomer')"><span class="color-black">转移成已有客户</span></el-dropdown-item>
                <el-dropdown-item v-if="props.showConfig.includes('customer2Person')" @click="handleTurnLead('person')"><span class="color-black">转移客户给他人</span></el-dropdown-item>
                <el-dropdown-item v-if="props.showConfig.includes('customer2Pool')" @click="handleTurnLead('pool')"><span class="color-black">{{ props.from === 'customerPool' ? '转移至其他公海' : '转移客户到客户公海'}}</span></el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    <TransferPool2Pool v-if="transferPool2PoolVisible" :visible="transferPool2PoolVisible" :from="props.from" :poolId="poolId" :checkedIds="selectedIds" @closeVisible="handleClose" />
    <TransferPool2Person v-if="transferPool2PersonlVisible" :visible="transferPool2PersonlVisible" :checkedIds="selectedIds" :poolId="poolId" :fromCustomer="props.from === 'customerPool'" @closeVisible="handleClose" />
    <TransferPerson2Pool v-if="transferPerson2PoolVisible" :visible="transferPerson2PoolVisible" :checkedIds="selectedIds" :fromCustomer="props.from === 'customerList'" @closeVisible="handleClose" />
    <TransferPerson2Person v-if="transferPerson2PersonVisible" :visible="transferPerson2PersonVisible" :checkedIds="selectedIds" :fromCustomer="props.from === 'customerList'" @closeVisible="handleClose" />
    <Transfer2ExistingCustomer :visible="transfer2ExistingCustomerVisible" :checkedIds="selectedIds" @closeVisible="handleClose" />

</template>
<style scoped lang='scss'>
</style>
