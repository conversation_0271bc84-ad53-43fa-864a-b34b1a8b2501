<script lang="ts" setup>
import { ref, inject } from 'vue'
import aicService from '@/service/aicService'
import type { Ref } from 'vue'
import type { ITradeMarkResponseItem } from '@/types/model'
import type { IGetTradeMarkGetDetail } from '@/types/aic'
import { parseTime } from '@/utils/parse-time'
const props = defineProps<{
    row: ITradeMarkResponseItem
    type?: string
}>()

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tradeMarkDetailVisible = ref(false)
const tradeMarkDetail = ref<IGetTradeMarkGetDetail>()
const handleOpenMARKIMGOSS = (row: ITradeMarkResponseItem) => {
    aicService
        .getGsGetDetail({
            id: row.APPLYNO,
            socialCreditCode: socialCreditCode.value,
            markTypeNum: row.MARKTYPENUM,
            moduleName: 'tradeMarkInfo',
        })
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                tradeMarkDetail.value = data as IGetTradeMarkGetDetail
                tradeMarkDetailVisible.value = true
            }
        })
}
</script>
<template>
    <div v-if="props.type" class="!color-blue pointer" @click="handleOpenMARKIMGOSS(props.row)">
        {{ props.row.MARKNAME || '-' }}
    </div>
    <el-image
        v-else
        style="width: 100%; height: 40px"
        :src="props.row.MARKIMGOSS"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :prediv-src-list="[props.row.MARKIMGOSS]"
        :initial-index="0"
        fit="contain"
        :prediv-teleported="true"
        :hide-on-click-modal="true"
        @click="handleOpenMARKIMGOSS(props.row)"
    >
        <template #error>
            <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
            </div>
        </template>
    </el-image>
    <el-dialog v-model="tradeMarkDetailVisible" title="商标详情" append-to-body style="height: 700px; overflow-y: auto">
        <div class="display-flex space-between">
            <div class="w-122 h-162 r-margin-24" style="border: 1px solid var(--border-color)">
                <el-image
                    style="width: 100%; height: 100%"
                    :src="props.row.MARKIMGOSS"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :prediv-src-list="[props.row.MARKIMGOSS]"
                    :initial-index="0"
                    fit="contain"
                    :prediv-teleported="true"
                    :hide-on-click-modal="true"
                    @click="handleOpenMARKIMGOSS"
                >
                    <template #error>
                        <div class="image-slot">
                            <el-icon><icon-picture /></el-icon>
                        </div>
                    </template>
                </el-image>
            </div>
            <div class="flex-1 font-14 color-two-grey" style="border: 1px solid var(--border-color)">
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商标名称</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.markName || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >注册号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.applyNo || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商标类别
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ props.row.MARKTYPE || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商标状态</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.status || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >专用权期限</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.deadline || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >申请日期</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ tradeMarkDetail?.applyDate ? parseTime(tradeMarkDetail?.applyDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商标类型
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.markTypeStr || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >申请人</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.companyName || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >申请人（英文）</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        tradeMarkDetail?.companyEnName || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >申请人地址
                        <el-tooltip
                            v-if="tradeMarkDetail?.addressChanged"
                            class="box-item"
                            effect="light" 
                            content="当前申请人地址与最新通讯地址不符，需尽快处理"
                            placement="top"
                        >
                            <Icon icon="icon-a-tishi1" size="16" color="#d9d9d9" class="pointer"></Icon>
                        </el-tooltip>
                        </el-col>
                    <el-col :span="18" class="all-padding-16" :class=" tradeMarkDetail?.addressChanged ? 'color-red' : ''" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.applyAddr || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >申请人地址（英文）</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        tradeMarkDetail?.applyEnAddr || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >代理机构</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.agentName || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >是否共有商标</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        tradeMarkDetail?.isCommonMark || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >国际注册日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">- 
                        {{ tradeMarkDetail?.interRegiDate ? parseTime(tradeMarkDetail?.interRegiDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >注册公告期号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.registerNo || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >注册公告日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        > {{ tradeMarkDetail?.registerDate ? parseTime(tradeMarkDetail?.registerDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >后期指定日期</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ tradeMarkDetail?.specifyDate ? parseTime(tradeMarkDetail?.specifyDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >优先权日期</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{ tradeMarkDetail?.priorityDate ? parseTime(tradeMarkDetail?.priorityDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >初审公告期号
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.examPubNo || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >初审公告日期</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                        >{{ tradeMarkDetail?.examPubDate ? parseTime(tradeMarkDetail?.examPubDate, '{y}-{m}-{d}') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商标动态</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div v-if="!tradeMarkDetail?.markStatus">-</div>
                        <div v-else>
                            <div v-for="(t, index) in JSON.parse(tradeMarkDetail?.markStatus)" :key="index">
                                {{ t }}
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                        >商品/服务项目</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div v-if="!tradeMarkDetail?.inventory">-</div>
                        <div v-else>
                            <div
                                class="b-margin-2"
                                v-for="(a, index) in (tradeMarkDetail?.inventory).match(/\d{4} [^0-9]+/g)"
                                :key="index"
                            >
                                {{ a }}
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <template #footer>
            <el-button @click="tradeMarkDetailVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
