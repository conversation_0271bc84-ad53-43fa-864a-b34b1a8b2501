<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'

import type { FormInstance, FormRules } from 'element-plus'
import type { ITenantPageItem, ITenantDataSourceParams } from '@/types/tenant'
import systemService from '@/service/systemService'

import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    type: string
    chooseTenant: Partial<ITenantPageItem>
}>()

watch(props, (newVal) => {
    if (newVal.visible && newVal.chooseTenant && newVal.type) {
        const { name } = newVal.chooseTenant
        form.name = name || ''
        if (newVal.type === 'dataSource') {
            getTenantGetAicChannel()
        } else if (newVal.type === 'collectWay') {
            getTenantGetTaxCollectType()
        }
    }
}, {
    deep: true
})
// 获取数据源配置
const getTenantGetAicChannel = async () => {
    try {
        let res = await systemService.tenantGetAicChannel({
            tenantId: props.chooseTenant.id as string
        })
        const { channelType } = res || {}
        if (channelType === '2') {
            form.channelType = 'xlb'
        } else if (channelType === '1') {
            form.channelType = 'lxy'
        }
    } catch (error) {
        console.log('获取数据源配置失败', error)
    }
}
// 获取采集通道配置
const getTenantGetTaxCollectType = async() => {
    try {
        let res = await systemService.tenantGetTaxCollectType({
            tenantId: props.chooseTenant.id as string
        })
        const { errCode, data } = res || {}
        if (errCode === 0) {
            const { type } = data || {}
            if (type) {
                form.type = type
            }  
        }
    } catch (error) {
        console.log('获取采集通道配置失败', error)
    }
}


const formRef = ref<FormInstance>()
const dialogVisible = computed(() => {
    return JSON.parse(JSON.stringify(props.visible))
})

const form = reactive<ITenantDataSourceParams>({
    name: '',
    tenantId: '',
    type: '',
    channelType: '',
})

const rules = reactive<FormRules<ITenantDataSourceParams>>({
    name: [{ required: true, message: '请输入租户名称', trigger: 'blur' }],
    type: [
        {
            required: true,
            message: '请选择采集通道',
            trigger: 'blur',
        },
    ],
    channelType: [
        {
            required: true,
            message: '请选择数据源',
            trigger: 'blur',
        },
    ],
})
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (props.type === 'collectWay') {
                let obj = {
                    type: form.type,
                    tenantId: props.chooseTenant?.id || '',
                }
                // 设置财税采集通道
                let res = await systemService.tenantSetTaxCollectType(obj)
                if (res.success === true) {
                    ElMessage.success('采集通道配置成功')
                } else {
                    ElMessage.error(res.errMsg)
                }
            } else {
                // 设置工商通道
                let obj = {
                    tenantId: props.chooseTenant?.id || '',
                    channelType: form.channelType,
                }
                let res = await systemService.tenantSetAicChannel(obj)
                if (res.success === true) {
                    ElMessage.success('数据源配置成功')
                } else {
                    ElMessage.error(res.errMsg)
                }
            }
            handleClose()
        } else {
            console.log('form表单效验不通过', fields)
        }
    })
}
const emit = defineEmits(['closeVisible'])
const handleClose = () => {
    form.name = ''
    form.tenantId = ''
    form.type = ''
    form.channelType = ''
    emit('closeVisible')
}
</script>
<template>
    <!-- 新增弹框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="props.type === 'dataSource' ? '数据源配置' : '采集通道配置'"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <el-form class="tenant-form" ref="formRef" :model="form" :rules="rules" label-position="top">
            <el-form-item label="租户名称" prop="name">
                <el-input v-model="form.name" :disabled="true" />
            </el-form-item>
            <el-form-item v-if="props.type === 'collectWay'" label="采集通道" prop="type">
                <el-select v-model="form.type">
                    <el-option label="RPA永久有效" value="RPAFOREVER">RPA永久有效</el-option>
                    <el-option label="RPA一码授权" value="RPA">RPA一码授权</el-option>
                    <el-option label="微风企" value="WFQ">微风企</el-option>
                    <el-option label="企享云" value="QXYV2">企享云</el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="props.type === 'dataSource'" label="整体数据源" prop="channelType">
                <el-select v-model="form.channelType">
                    <el-option label="励销云" value="lxy">数据-L</el-option>
                    <el-option label="小蓝本" value="xlb">数据-X</el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss">
.tenant-form {
    :deep(.el-form-item__label) {
        font-size: 16px;
        color: #666666;
    }
}
</style>
