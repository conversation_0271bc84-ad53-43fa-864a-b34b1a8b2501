<template>
    <div class="tb-padding-16 lr-padding-24">
        <div v-if="isLoading">
            <div class="display-flex top-bottom-center">
                <div class="font-28 font-weight-600 color-gradinent-blue pointer" v-copy>
                    {{ companyBaseInfo.name }}
                </div>
                <div class="l-margin-16">
                    <el-button type="primary"
                        v-if="companyClueInfo.clueType === 0 && permissionService.isTransferNewLeadPermitted()"
                        @click="handleAddCrm">转线索</el-button>
                    <div v-if="companyClueInfo.clueType !== 0" class="display-flex top-botton-cneter">
                        <div class="border-radius-4 border-main-color tb-padding-5 lr-padding-12 font-14 color-blue">
                            {{ clueStr }}

                        </div>
                        <div class="display-flex top-bottom-cneter l-margin-10 tb-padding-6 pointer font-14"
                            @click="jump2Location">
                            <div>
                                <Icon icon="icon-a-huaban64" :size="16" style="margin-right: 4px"></Icon>
                            </div>
                            <div class="color-blue font-14" style="vertical-align: middle">{{ crmLocation }}</div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="flex font-16 t-margin-16">
                <el-row class="b-margin-12 width-100">
                    <el-col :span="8"><span class="color-two-grey">法定代表人：</span> {{ companyBaseInfo.legalperson
                    }}</el-col>
                    <el-col :span="8">
                        <span class="color-two-grey">成立日期：</span>
                        {{ companyBaseInfo.esdate || '-' }}
                    </el-col>
                    <!-- <el-col :span="8"><span class="color-two-grey">注册资本:</span>{{ companyBaseInfo.regCapDisplay
                    }}</el-col> -->
                    <el-col :span="8"><span class="color-two-grey">税号：</span>
                        <span class="pointer t-margin-3" v-copy>
                            {{ socialCreditCode }}
                            <Icon class="l-margin-5" icon="icon-fuzhi" :size="16" color="" />
                        </span>
                    </el-col>
                </el-row>
                <!-- <el-row class="b-margin-12"> -->
                <!-- <el-col :span="8"><span class="color-two-grey">网站:</span>{{ companyBaseInfo.officialWebsite
                    }}</el-col> -->

                <!-- </el-row> -->
                <!-- <el-row class="b-margin-12">
                    <el-col :span="24"><span class="color-two-grey">通讯地址:</span> {{ companyBaseInfo.contactaddress
                    }}</el-col>
                </el-row> -->
            </div>
            <div class="display-flex t-margin-8 flex-wrap">
                <div v-for="(item, index) in companyBaseInfo.tags" v-show="index <= 10" class="b-margin-6" :key="index">
                    <el-tag class="r-margin-12" :type="item.categoryCode == '001' ? 'success' : 'warning'"
                        effect="plain">
                        {{ item.tagName }}
                    </el-tag>
                </div>
            </div>
        </div>
        <div v-else>
            <el-skeleton :rows="5" animated />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, inject, defineEmits, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { CompanyBaseInfo } from '@/types/company'
import type { GsGetCompanyClueInfoResponse } from '@/types/lead'
import eventBus from '@/utils/eventBus'
import aicService from '@/service/aicService'
import crmService from '@/service/crmService'
import type { Ref } from 'vue'
import { useRouter } from 'vue-router'

import permissionService from '@/service/permissionService'

const router = useRouter()
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const buyStatus: Ref<boolean> = inject('buyStatus', ref(false))

const companyClueInfo: Ref<GsGetCompanyClueInfoResponse> = inject('companyClueInfo', ref({} as GsGetCompanyClueInfoResponse))

const companyBaseInfo: Ref<CompanyBaseInfo> = ref({} as CompanyBaseInfo)

const isLoading = ref(false)

const emits = defineEmits(['updateBuyStatus', 'updateCompanyInfo'])


const handleAddCrm = () => {

    console.log('buyStatus', buyStatus)
    if (buyStatus.value) {
        addCrm()
    } else {
        ElMessageBox.confirm('转线索将会扣除对应线索权益额度，是否确定使用？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(async () => {
            addCrm()
        })
    }

}

const addCrm = () => {
    crmService.crmAdd({
        clueType: 2,
        socialCreditCode: socialCreditCode.value,
        companyName: companyBaseInfo.value.name,
        source: 1
    }).then(res => {
        console.log('rrrr', res)
        ElMessage.success('转移成功')
        // if (res.errCode !== 0) {
        //     ElMessage.error(`转线索失败:${res.errMsg}`)
        //     return
        // }
        // aicService.gsGetCompanyClueInfo({ socialCreditCode: socialCreditCode.value }).then((getCrmUserRes) => {
        //     companyClueInfo.value = getCrmUserRes.data
        // })

        eventBus.$emit('refreshBuyStatus')
    })
}

const init = () => {
    let getBase = aicService.gsGetCompanyBaseInfo({
        socialCreditCode: socialCreditCode.value,
    })

    let getCompanyByCode = aicService.searchEnterprise({ keyword: socialCreditCode.value, scope: 'unicode' })



    Promise.all([getBase, getCompanyByCode]).then(([getBaseRes, getCompanyByCodeRes]) => {
        isLoading.value = true
        // console.log('getCompanyByCodeRes', getCompanyByCodeRes)
        if (Array.isArray(getCompanyByCodeRes.data) && getCompanyByCodeRes.data.length > 0) {
            companyBaseInfo.value = {
                ...getCompanyByCodeRes.data[0],
            }
        }
        companyBaseInfo.value.tags = getBaseRes.tags
        emits('updateCompanyInfo', companyBaseInfo.value)
    })
}


const clueStr = computed(() => {
    let clue = companyClueInfo.value
    if (clue.leadPoolName) {
        return `线索池 : ${clue.leadPoolName}`
    } else if (clue.customerPoolName) {
        return `客户公海 : ${clue.customerPoolName}`
    } else if (clue.username) {
        return `负责人 : ${clue.username}`
    } else {
        return '-'
    }

})

const crmLocation = computed(() => {
    let clue = companyClueInfo.value

    if (clue.clueType === 1 && clue.leadPoolName) {
        return '线索池'
    } else if (clue.clueType === 1) {
        return '线索列表'
    } else if (clue.clueType === 2 && clue.customerPoolName) {
        return '客户公海'
    } else if (clue.clueType === 2) {
        return '客户列表'
    } else {
        return '-'
    }
})

const jump2Location = () => {
    let clue = companyClueInfo.value
    if (clue.clueType === 1 && clue.leadPoolName) {
        router.push({ name: 'su-crm-lead-pool' })
    } else if (clue.clueType === 1) {
        router.push({ name: 'su-crm-lead-list' })
    } else if (clue.clueType === 2 && clue.customerPoolName) {
        router.push({ name: 'crm-customer-poll' })
    } else if (clue.clueType === 2) {
        router.push({ name: 'su-crm-customer-list' })
    } else {
        return
    }
}

onMounted(() => {
    if (socialCreditCode) {
        init()
        eventBus.$on('refreshBuyStatus', () => {
            init()
        })
    }
})
</script>

<style lang='scss' scoped>
.border-main-color {
    border: 1px solid var(--el-color-primary);
}
</style>