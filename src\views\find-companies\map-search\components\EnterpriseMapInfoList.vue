<template>
    <div class="search-list">
        <div class="header-bar">
            <el-dropdown v-if="permissionService.isTransferNewLeadPermitted()" class="batch-transfer">
                <el-button>转CRM
                    <el-icon class="el-icon--right">
                        <CaretBottom />
                    </el-icon>
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="transferCheck()">转移所选</el-dropdown-item>
                        <el-dropdown-item @click="getTransferList(20)">转前20条</el-dropdown-item>
                        <el-dropdown-item @click="getTransferList(40)">转前40条</el-dropdown-item>
                        <el-dropdown-item @click="getTransferList(60)">转前60条</el-dropdown-item>
                        <el-dropdown-item @click="getTransferList(80)">转前80条</el-dropdown-item>
                        <el-dropdown-item @click="getTransferList(100)">转前100条</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <div class="result">

                <div class="result-scope l-padding-16"><el-checkbox class="r-margin-5" v-model="checkAll"
                                                                    :indeterminate="isIndeterminate" @change="handleCheckAllChange">

                </el-checkbox> 每页<span class="page-total">{{ pageInfo.pageSize }}</span>共<span
                    class="result-total">{{
                    listTotal }}</span>个
                </div>
                <div class=" result-scope">搜索半径：{{ rd }}km</div>
            </div>
        </div>
        <div class="content-wrap" v-if="companyList.length > 0">
            <ul class="list-wrap ">
                <el-checkbox-group v-model="checkList" @change="handleCheckedChange">
                    <li class="list-item pointer" v-for="(item, index) in companyList" @click="companyClick(item)"
                        :key=item.socialCreditCode @mouseover="hoverIndex = index" @mouseout="hoverIndex = -1"
                        :class="{ 'selected': index == hoverIndex }">
                        <div class="list-item-title display-flex space-between top-bottom-center">
                            <div>
                                <el-checkbox :key="index" :value="item" @click="companyClick(item)">

                                </el-checkbox>
                            </div>
                            <div class="flex-1 font-500 font-16" @click.stop="showCompanyDetail(item)">
                                {{ item.companyName }}
                            </div>
                            <div class="list-item-status font-14 text-nowrap"
                                 :class="['注销', '吊销'].includes(item.entstatus.slice(0, 2)) ? 'warn' : (item.entstatus == '' ? 'hide' : '')">
                                {{
                                    item.entstatus.slice(0, 2) }}
                            </div>
                        </div>
                        <div class="display-flex color-two-grey font-14">
                            <div class="info flex-1">
                                <p class="list-item-line">
                                    <span>{{ item.legalperson }}</span>
                                    <span class="list-item-seperator">|</span>
                                    <span>{{ item.esdate || '-' }}</span>
                                    <span class="list-item-seperator">|</span>
                                    <span v-if="item.regCapDisplay">{{ item.regCapDisplay }}</span>
                                    <span v-else>-</span>
                                </p>
                                <p class="list-item-address list-item-line" :class="{ 'hoverBg': index == hoverIndex }">
                                    <Icon icon="icon-a-huaban64" color="#666666" size="16px" />

                                    {{ item.contactaddress }}
                                </p>
                                <p class="list-item-distance list-item-line"
                                   :class="{ 'hoverBg1': index == hoverIndex }">
                                    <Icon icon="icon-a-huaban65" color="#666666" size="16px" />
                                    距离搜索点{{ item.toCenterDistance }}km
                                </p>
                            </div>
                            <div class="info-btn display-flex flex-column">
                                <div class="btn" @click.stop='showCompanyDetail(item)'>
                                    详情
                                </div>
                            </div>

                        </div>

                    </li>
                </el-checkbox-group>
            </ul>
            <el-pagination style="margin-top: 10px;" pager-count='3' v-model:current-page="pageInfo.page"
                           v-model:page-size="pageInfo.pageSize" layout="prev, pager, next, jumper"
                           :total="listTotal > 3000 ? 3000 : listTotal" @current-change="handleCurrentChange" background small
                           v-show="listTotal > 20" />
        </div>
        <div class="no-result" v-else>
            <el-empty description="暂无相关数据" />
        </div>
        <CompanyDetaiDrawer v-model:drawer="companyDetailDrawerVisible" v-if="companyDetailDrawerVisible"
                            :socialCreditCode="ckCompanyItem.socialCreditCode" @refreshList="emits('pageChange', pageInfo.page)" />
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch, defineEmits, defineExpose } from 'vue'
import type { Ref } from 'vue'

import type { ISearchCompanyItem } from '@/types/company'
import Icon from '@/components/common/Icon.vue'

import CompanyDetaiDrawer from '@/components/search/company-detail-drawer/company-detail-drawer.vue'
import { ElMessage } from 'element-plus'
import permissionService from '@/service/permissionService'

const emits = defineEmits(['pageChange', 'getTransferList', 'transferCheck'])

const checkAll = ref(false)
const isIndeterminate = ref(false)
const ckCompanyItem: Ref<ISearchCompanyItem> = ref({} as ISearchCompanyItem)
const companyDetailDrawerVisible: Ref<boolean> = ref(false)
const showCompanyDetail = (item: ISearchCompanyItem) => {
    if (!item.socialCreditCode) {
        ElMessage({
            type: 'error',
            message: '该企业暂无详情',
        })
        return
    }
    ckCompanyItem.value = item
    companyDetailDrawerVisible.value = true
}

const hoverIndex: Ref<number> = ref(0)

const props = defineProps<{
	resultList: ISearchCompanyItem[],
	radius: number,
	listTotal: number
}>()

const companyList: Ref<ISearchCompanyItem[]> = ref(props.resultList)
watch(() => props.resultList, (newVal) => {
    companyList.value = newVal
})


const rd: Ref<number> = ref(5)
watch(() => props.radius, (newVal) => {
    rd.value = newVal
})

const pageInfo = ref({ page: 1, pageSize: 20 })


const handleCurrentChange = (page: number) => {
    emits('pageChange', page)
}

const checkList: Ref<ISearchCompanyItem[]> = ref([])

const handleCheckAllChange = (val: ISearchCompanyItem[]) => {
    checkList.value = val ? companyList.value : []
    isIndeterminate.value = false
}

const handleCheckedChange = (value: ISearchCompanyItem[]) => {
    const checkedCount = value.length
    checkAll.value = checkedCount === companyList.value.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < companyList.value.length
}

const getTransferList = async (pageSize: number) => {
    emits('getTransferList', pageSize)
}

const transferCheck = () => {
    emits('transferCheck', checkList.value)
}

const companyClick = (item: ISearchCompanyItem) => {
    let res = checkList.value.find(i => { return i.socialCreditCode === item.socialCreditCode })
    if (res) {
        checkList.value = checkList.value.filter(i => { return i.socialCreditCode !== item.socialCreditCode })
    } else {
        checkList.value.push(item)
    }
    const checkedCount = checkList.value.length
    checkAll.value = checkedCount === companyList.value.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < companyList.value.length
}
const clearCheck = () => {
    checkList.value = []
    handleCheckedChange([])
}

defineExpose({
    clearCheck,
    pageInfo
})


</script>

<style lang="scss">
ul {
	list-style-type: none;
	padding: 0;
}

.search-list {
	background-color: white;
	height: calc(100vh - 48px - 40px);
	width: 444px;
	padding: 10px 0px;
	box-sizing: border-box;

	.header-bar {
		padding: 10px 10px;
		box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.05);


		.batch-transfer {
			margin-bottom: 6px;
		}

		.result {
			display: flex;
			justify-content: space-between;
			height: 40px;
			line-height: 35px;

			.result-total {
				color: var(--main-blue-);
			}

			.page-total {
				border-right: 1px solid var(--border-color);
				padding: 0 5px;
				margin-right: 5px;
				color: var(--main-blue-);
			}
		}
	}

	.content-wrap {
		height: calc(100% - 78px);
		white-space: normal;
		word-wrap: break-word;

		.list-wrap {
			max-height: calc(100% - 50px);
			overflow: auto;
			margin-bottom: 0;

			.list-item {
				position: relative;
				margin: 8px 8px 8px 10px;
				padding: 16px;
				border: 1px solid #d9d9d9;
				border-radius: 2px;
				line-height: 1.4;

				.list-item-title {
					margin-bottom: 18px;
					color: #333;
					font-weight: 700;

					.name {
						white-space: normal;
						word-wrap: break-word;
						line-height: 19px;
					}

					.list-item-status {
						width: 42px;
						padding: 0 6px;
						line-height: 24px;
						height: 24px;
						border-radius: 2px;
						border: 1px solid #52c41a;
						color: #52c41a;
						font-weight: 400;
					}

					.warn {
						border: 1px solid #ec6459;
						color: #ec6459;
					}

					.hide {
						display: none
					}
				}

				.info {
					font-size: 14px;

					.list-item-line {
						overflow: hidden;
						margin: 8px 0 0;
						color: #595959;

						.list-item-seperator {

							display: inline-block;
							margin: 0 4px;
							vertical-align: top;
							font-size: 12px;
						}
					}

				}

				.info-btn {
					width: 100px;
					justify-content: flex-end;
				}
			}

			.selected {
				border: 1px solid #1966ff;
				box-shadow: 0px 0px 8px rgba(179, 204, 255, 1);
			}
		}

		:deep(.el-pagination) {

			.el-pager {
				li {
					padding: 0;
					width: 5px
				}
			}

			.btn-next,
			.btn-prev {
				border: none
			}

			.el-pagination__jump,
			.el-pagination__classifier,
			.el-pagination__goto {
				margin: 3px
			}

			.el-input {
				width: 35px;

				.el-input__wrapper {
					padding: 0
				}
			}
		}
	}

	.no-result {
		margin-top: 100px;
		font-weight: 700;
	}
}
</style>