import type { IAllRecord } from '@/types/record'
import type { IPaginationResponse, ICommonResponse } from './axios'
import type { ISearchConditions, DraftingUnitsType } from '@/types/model'
import type { ISearchGetTemplateItem } from '@/types/company'

export interface Region extends IAicConditionDataOptionItem {
    firstLetter?: string
    parent?: Region
}

export interface IAicRegionResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: Region[]
}

export interface IAicConditionDataRequest extends IAllRecord {
    key?: string
}

export interface IAicConditionData {
    b2bMarketType: IAicConditionDataOptionItem[]
    caseReason: IAicConditionDataOptionItem[]
    certType: IAicConditionDataOptionItem[]
    area: IAicConditionDataOptionItem[]
    companySiteSourceMap: IAicConditionDataOptionItem[]
    contactSource: IAicConditionDataOptionItem[]
    cusIndusCategory: IAicConditionDataOptionItem[]
    fixedSource: IAicConditionDataOptionItem[]
    industry: IAicConditionDataOptionItem[]
    industryEn: IAicConditionDataOptionItem[]
    latest3MonthCertL2Type: IAicConditionDataOptionItem[]
    mobileSource: IAicConditionDataOptionItem[]
    serviceNode: IAicConditionDataOptionItem[]
    tenderProjectType: IAicConditionDataOptionItem[]
    tradeMarkCategory: IAicConditionDataOptionItem[]
}

export interface IAicConditionDataOptionItem {
    children?: IAicConditionDataOptionItem[]
    label: string
    pinyin: string
    value: string
}

export interface IAicNormalSearchRulesSysResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: IAicNormalSearchRules[]
}

export interface IAicNormalSearchRules {
    dataType: 'multiSelect' | 'select' | 'mapped' | 'area' | 'mulipleProvince'
    key: string
    name: string
    enums: IAicNormalSearchRuleEnums[]
    needSearch?: string
    paramType?: string
}

export interface IAicNormalSearchRuleEnums {
    name: string
    tagValue: string
}

export interface INormalFilterParams {
    label: string
    value: string
    category?: string
    type?: 'multiSelect' | 'select' | 'mapped' | 'area' | 'mulipleProvince'
    categoryKey: string
    checked?: boolean
    name?: string
    num?: string
    pinyin?: string
    paramType?: string
}

export interface AddProjectParams {
    categoryId?: string
    name: string
    parentId?: string
    shortName?: string
}

export interface ISearchListParams extends IAllRecord {
    page: number
    pageSize: number
    id?: string
}

export interface ModelGetCategoryListResponse extends IPaginationResponse {
    data: CatagoryItem[]
}

export interface CatagoryItem {
    id: string
    name: string
    parentId?: string
    shortName?: string
    createTime?: string
    level?: number
    isNormal?: boolean
    disabled?: boolean
    collapsed?: boolean
    children?: CatagoryItem[]
}

export interface CategoryItemResponse extends ICommonResponse {
    data: CatagoryItem[]
}

export interface SaveTempleteParams {
    name?: string
    categoryIds?: string[]
    searchData?: {
        list: ISearchConditions[]
    }
}

export interface UpdateTemplateParams extends SaveTempleteParams {
    templateId: string
    useTag?: boolean
    sort?: number
}

export interface GsGetPersonEnterpriseRelationsParams extends IAllRecord {
    entId: string
    name: string
    companyName: string
    page: number
}

export interface PersonEnterpriseRelationsItem extends IAllRecord {
    address: string
    entName: string
    // entTags: {
    //     tagName: string
    //     category: string
    //     tagCode: string
    //     categoryCode: string
    // }[]
    pid: string
    position: string
    positionStatus: string
    regCapital: string
    shareholdingRatio: string
}

export interface GsGetPersonEnterpriseRelationsResponse {
    data: {
        items: PersonEnterpriseRelationsItem[]
        total: number
    }
}

export interface ICenterEntParams {
    circle: {
        center: string
        radius: number
    }
    contact?: string[]
    entstatus?: string[]
    enttype?: string[]
    establishment?: string[]
    industry?: string[]
    keyword?: string
    location?: string[]
    page: number
    pageSize: number
    registercapital?: string[]
    scope?: string
}

export interface ICenterEntResponse {
    data: []
    page: number
    pageSize: number
    total: number
}

export interface ISearchCopyTemplateParams {
    templateId: string
    toUsers: string[]
}

export interface ISearchGetEntTemplateItem {
    categoryIds: string[]
    createUserName: string
    create_date: string
    id: string
    isDel: number
    isModel: boolean
    isShare: boolean
    name: string
    remark: string
    searchData: {
        list: ISearchConditions[]
        valueTypeEnable: boolean
    }
    shareTime: number
    sort: string
    tenantId: string
    update_date: string
    useNum: number
    userId: string
}
export interface ISearchGetEntTemplateResponse extends IPaginationResponse {
    data: ISearchGetTemplateItem[]
}

export interface IGetGsGetDetailParams extends IAllRecord {
    id?: string
    pid?: string
    socialCreditCode?: string
    markTypeNum?: string
    moduleName?: string
    section?: string
    page?: number
    rankPage?: number
}

export interface IGetTradeMarkGetDetail {
    addressChanged: boolean
    agentName: string
    applyAddr: string
    applyDate: number
    applyEnAddr: string
    applyNo: string
    companyEnName: string
    companyName: string
    curStatus: string
    deadline: string
    examPubNo: string
    interRegiDate: string
    inventory: string
    isCommonMark: string
    markImgOss: string
    markName: string
    markStatus: string
    markStatusClean: string
    markTypeNum: string
    markTypeStr: string
    priorityDate: string
    registerNo: string
    socialCreditCode: string
    specifyDate: string
    status: string
    registerDate: number
    examPubDate: number
}
export interface IGetStandardInfoOverView {
    items: DraftingUnitsType[]
    total: number
}

export interface IDomainKeywordsItem{
    keyword: string
    page: number
    platform: string
    position: number
    rank: string
    type: string
    _id: string
}
export interface IIpInfo{
    domain: string
    ip: string
    serverPhysicNode: string
    serviceNode: string
}
export interface IEeo{
    RANK: string
    seoDescription: string
    seoKeyWords: string
    seoTitle: string
    totalCollectionBaidu: number
    totalCollectionQihoo: number
    totalCollectionSougou: number
    weightPcBaidu: number
    weightPcQihoo: number
    weightPcSougou: number
}
export interface IWebModule{
    devLanguage: string
    hasFlashModule: string
    hasMobileSite: string
    isRespTech: string
    lastChangeDate: number
    onlineChat: string
    recordAge: string
    standardOffset: string
    templateSuppiler: string
    webFeature: string
    websiteSpeed: string
}

export interface IWebSecureIndex{
    baiduCheat: string
    baiduFalsified: string
    baiduIllegal: string
    baiduTrojan: string
    deadLinkCount: number
    hasSsl: string
    illegalLinkCount: number
}
export interface IGetWebsiteDetailInfo{
    domainKeywords: {
        items: IDomainKeywordsItem[]
        total: number
    }
    ipInfo: {
        items:IIpInfo[]
        total:number
    }
    seo: IEeo
    webModule: IWebModule
    webSecureIndex:IWebSecureIndex
}
export interface IDomainKeywords{
    domainKeywords: {
        items: IDomainKeywordsItem[]
        total: number
    }
}
export interface IGetGsGetDetailResponse extends ICommonResponse {
    data: IGetTradeMarkGetDetail | IGetStandardInfoOverView | IGetWebsiteDetailInfo | IDomainKeywords
}
