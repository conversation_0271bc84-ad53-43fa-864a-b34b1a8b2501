<template>
    <el-drawer 
        v-model="drawerVisible" 
        size="70%"
    >
        <template #header>
            <span class="font-20 color-black">
                消费明细
            </span>
        </template>
        <div class="border b-margin-16"></div>
        <div class="display-flex flex-column gap-16">
            <searchBox 
                searchOptionKey="BENEFIT_RECORD_SEARCH_OPTIONS"
                @updateSearchParams="updateSearchParams"
                :defaultValue="defaultQueryParams"
                :customConfig="searchConfig"
            />
            <div class="flex flex-row b-margin-16">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="exportRecord()">导出全部</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;min-height: 40%;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="权益名称" min-width="100" >
                    <template #default="scope">
                        {{ scope.row.serviceName }}
                    </template>
                </el-table-column>
                <el-table-column label="企业名称" prop="companyName" min-width="50" >
                </el-table-column>
                <el-table-column label="企业税号" prop="socialCreditCode" min-width="60" >
                </el-table-column>
                <el-table-column label="类型" prop="usageType" min-width="50">
                    <template #default="scope">
                        <span v-if="scope.row.usageType === 0">回退</span>
                        <span v-else-if="scope.row.usageType === 1">扣减</span>
                        <span v-else-if="scope.row.usageType === 2">预扣减</span>
                        <span v-else>其他</span>
                    </template>
                </el-table-column>
                <el-table-column label="数量" prop="consumeAmount" min-width=“50” />
                <el-table-column label="操作员" prop="operatorName" min-width=“100” />
                <el-table-column label="消费时间" prop="usageTime" min-width=“150” >
                    <template #default="scope">
                        {{ scope.row.usageTime ? moment(scope.row.usageTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
            </el-table>            
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>   
        </div>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, watch, getCurrentInstance, onBeforeMount,nextTick } from 'vue'
import searchBox from '@/components/common/SearchBox.vue'
import type { IOrderUsageRecordParams, IOrderUsageRecordResponseItem } from '@/types/order'
import orderService from '@/service/orderService'
import { ElMessage } from 'element-plus'
import { downloadFile } from '@/utils/download'
import systemService from '@/service/systemService'
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const exporting = ref(false)
const props = defineProps<{
    serviceKey: string
    tenantId: string
    visible: boolean
    transId: string
}>()
const defaultQueryParams = ref<Record<string, boolean | string | number[] | string[]>>({
})
const emit = defineEmits(['update:visible'])
const drawerVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

watch(() => props.visible, (newVal) => {
    console.log('props.serviceKey', props.serviceKey)
    console.log('props.tenantId', props.tenantId)
    if (newVal) {
        nextTick(()=>{
            defaultQueryParams.value = ({
                'serviceKey': props.serviceKey,
                'tenantId': props.tenantId,
                'transId':props.transId
            })
            console.log('defaultQueryParams', defaultQueryParams.value)
        })
       
    }
})

const tableLoading = ref<boolean>(false)
const tableData = ref<IOrderUsageRecordResponseItem[]>([])
const getBenefitRecord = (Params: IOrderUsageRecordParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    orderService.orderServiceUsagePage(Params).then((res) => {
        console.log(res)
        if(res.success){
            pageInfo.total = res.total
            tableData.value = res.data
        }else{
            ElMessage.error('系统错误')
        }
    }).then(() => {
        tableLoading.value = false
    })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitRecord(queryParams.value)
}

const updateSearchParams = (params: IOrderUsageRecordParams) =>{
    queryParams.value = params
    getBenefitRecord(queryParams.value)
}

const queryParams = ref<IOrderUsageRecordParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
    serviceKey: props.serviceKey,
    tenantId: props.tenantId,
    transId:props.transId
})


const exportRecord = () => {
    let params = {} as IOrderUsageRecordParams
    orderService.orderUsageExport(params)
        .then((res) => {
            if(res.data.type === 'application/vnd.ms-excel'){
                exporting.value = true
                downloadFile(res)
                exporting.value = false
            }
            else{
                ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
            }
        })
        .catch(() => {
            exporting.value = false
            ElMessage.warning('导出失败，请稍后再试')
        })
}

onBeforeMount(() => {
})
onMounted(() => {
    systemService.userGetUserByScopeData('collect').then(response => {
        searchConfig.value = {
            ...searchConfig.value,
            operatorId:response.data.map(item => ({ 
                value: item.id,
                label: item.nickname 
            }))
        }
    })
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>
